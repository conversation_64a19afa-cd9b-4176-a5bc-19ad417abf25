const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * Middleware to authenticate JWT tokens
 * Adds user object to req.user if token is valid
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided or invalid format.'
      });
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Token is valid but user not found'
      });
    }
    
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }
    
    // Add user to request object
    req.user = user;
    next();
    
  } catch (error) {
    console.error('Authentication error:', error.message);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error during authentication'
    });
  }
};

/**
 * Middleware to check if user is trip participant
 * Must be used after authenticate middleware
 */
const checkTripAccess = (model = 'Trip') => {
  return async (req, res, next) => {
    try {
      const tripId = req.params.tripId || req.params.id;
      
      if (!tripId) {
        return res.status(400).json({
          success: false,
          message: 'Trip ID is required'
        });
      }
      
      let trip;
      if (model === 'Trip') {
        const Trip = require('../models/Trip');
        trip = await Trip.findById(tripId);
      } else if (model === 'Activity') {
        const Activity = require('../models/Activity');
        const activity = await Activity.findById(tripId).populate('trip');
        trip = activity ? activity.trip : null;
      }
      
      if (!trip) {
        return res.status(404).json({
          success: false,
          message: 'Trip not found'
        });
      }
      
      // Check if user is creator or participant
      const isCreator = trip.creator.toString() === req.user._id.toString();
      const isParticipant = trip.participants.some(p => 
        p.user.toString() === req.user._id.toString() && p.status === 'joined'
      );
      
      if (!isCreator && !isParticipant) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You are not a participant of this trip.'
        });
      }
      
      // Add trip to request object
      req.trip = trip;
      req.isCreator = isCreator;
      
      next();
      
    } catch (error) {
      console.error('Trip access check error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Server error during access check'
      });
    }
  };
};

/**
 * Middleware to check if user can edit trip/activity
 * Must be used after authenticate and checkTripAccess middleware
 */
const checkEditPermission = async (req, res, next) => {
  try {
    if (!req.trip) {
      return res.status(400).json({
        success: false,
        message: 'Trip context not found'
      });
    }
    
    // Creator can always edit
    if (req.isCreator) {
      return next();
    }
    
    // Check participant permissions
    const participant = req.trip.participants.find(p => 
      p.user.toString() === req.user._id.toString()
    );
    
    if (!participant || !participant.permissions.canEdit) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to edit this trip'
      });
    }
    
    next();
    
  } catch (error) {
    console.error('Edit permission check error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Server error during permission check'
    });
  }
};

/**
 * Middleware to check if user can invite others to trip
 * Must be used after authenticate and checkTripAccess middleware
 */
const checkInvitePermission = async (req, res, next) => {
  try {
    if (!req.trip) {
      return res.status(400).json({
        success: false,
        message: 'Trip context not found'
      });
    }
    
    // Creator can always invite
    if (req.isCreator) {
      return next();
    }
    
    // Check participant permissions
    const participant = req.trip.participants.find(p => 
      p.user.toString() === req.user._id.toString()
    );
    
    if (!participant || !participant.permissions.canInvite) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to invite users to this trip'
      });
    }
    
    next();
    
  } catch (error) {
    console.error('Invite permission check error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Server error during permission check'
    });
  }
};

/**
 * Optional authentication middleware
 * Adds user to req.user if token is provided and valid, but doesn't fail if no token
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }
    
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');
    
    if (user && user.isActive) {
      req.user = user;
    }
    
    next();
    
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

module.exports = {
  authenticate,
  checkTripAccess,
  checkEditPermission,
  checkInvitePermission,
  optionalAuth
};
