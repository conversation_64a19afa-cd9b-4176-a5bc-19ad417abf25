const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  trip: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trip',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: [true, 'Message content is required'],
    trim: true,
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  type: {
    type: String,
    enum: ['text', 'image', 'file', 'system', 'activity', 'poll'],
    default: 'text'
  },
  metadata: {
    // For system messages
    action: {
      type: String,
      enum: ['user_joined', 'user_left', 'trip_updated', 'activity_added', 'activity_voted']
    },
    // For activity-related messages
    activityId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Activity'
    },
    // For file/image messages
    attachment: {
      filename: String,
      originalName: String,
      mimetype: String,
      size: Number,
      url: String
    },
    // For polls
    poll: {
      question: String,
      options: [{
        text: String,
        votes: [{
          user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
          },
          votedAt: {
            type: Date,
            default: Date.now
          }
        }]
      }],
      allowMultiple: {
        type: Boolean,
        default: false
      },
      expiresAt: Date
    }
  },
  readBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  reactions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    emoji: {
      type: String,
      required: true
    },
    reactedAt: {
      type: Date,
      default: Date.now
    }
  }],
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  edited: {
    isEdited: {
      type: Boolean,
      default: false
    },
    editedAt: Date,
    originalContent: String
  },
  deleted: {
    isDeleted: {
      type: Boolean,
      default: false
    },
    deletedAt: Date,
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for unread count by user
messageSchema.virtual('isReadBy').get(function() {
  return (userId) => {
    return this.readBy.some(read => read.user.toString() === userId.toString());
  };
});

// Virtual for reaction count by emoji
messageSchema.virtual('reactionCounts').get(function() {
  const counts = {};
  this.reactions.forEach(reaction => {
    counts[reaction.emoji] = (counts[reaction.emoji] || 0) + 1;
  });
  return counts;
});

// Virtual for total reactions
messageSchema.virtual('totalReactions').get(function() {
  return this.reactions ? this.reactions.length : 0;
});

// Indexes for better query performance
messageSchema.index({ trip: 1, createdAt: -1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ type: 1 });
messageSchema.index({ 'readBy.user': 1 });
messageSchema.index({ replyTo: 1 });

// Compound index for trip messages with pagination
messageSchema.index({ trip: 1, createdAt: -1, _id: -1 });

// Instance method to mark as read by user
messageSchema.methods.markAsRead = function(userId) {
  const alreadyRead = this.readBy.some(read => 
    read.user.toString() === userId.toString()
  );
  
  if (!alreadyRead) {
    this.readBy.push({
      user: userId,
      readAt: new Date()
    });
  }
};

// Instance method to add reaction
messageSchema.methods.addReaction = function(userId, emoji) {
  const existingReaction = this.reactions.find(reaction => 
    reaction.user.toString() === userId.toString() && reaction.emoji === emoji
  );
  
  if (!existingReaction) {
    this.reactions.push({
      user: userId,
      emoji: emoji,
      reactedAt: new Date()
    });
  }
};

// Instance method to remove reaction
messageSchema.methods.removeReaction = function(userId, emoji) {
  this.reactions = this.reactions.filter(reaction => 
    !(reaction.user.toString() === userId.toString() && reaction.emoji === emoji)
  );
};

// Instance method to edit message
messageSchema.methods.editContent = function(newContent) {
  if (!this.edited.isEdited) {
    this.edited.originalContent = this.content;
  }
  this.content = newContent;
  this.edited.isEdited = true;
  this.edited.editedAt = new Date();
};

// Instance method to soft delete
messageSchema.methods.softDelete = function(deletedBy) {
  this.deleted.isDeleted = true;
  this.deleted.deletedAt = new Date();
  this.deleted.deletedBy = deletedBy;
  this.content = '[Message deleted]';
};

// Static method to find trip messages with pagination
messageSchema.statics.findTripMessages = function(tripId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return this.find({ 
    trip: tripId, 
    'deleted.isDeleted': { $ne: true } 
  })
    .populate('sender', 'name email profilePhoto')
    .populate('replyTo', 'content sender')
    .populate('reactions.user', 'name')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get unread message count for user
messageSchema.statics.getUnreadCount = function(tripId, userId) {
  return this.countDocuments({
    trip: tripId,
    'deleted.isDeleted': { $ne: true },
    'readBy.user': { $ne: userId },
    sender: { $ne: userId } // Don't count own messages
  });
};

// Static method to mark all messages as read for user
messageSchema.statics.markAllAsRead = async function(tripId, userId) {
  const messages = await this.find({
    trip: tripId,
    'deleted.isDeleted': { $ne: true },
    'readBy.user': { $ne: userId },
    sender: { $ne: userId }
  });
  
  const updatePromises = messages.map(message => {
    message.markAsRead(userId);
    return message.save();
  });
  
  return Promise.all(updatePromises);
};

// Static method to create system message
messageSchema.statics.createSystemMessage = function(tripId, action, metadata = {}) {
  return this.create({
    trip: tripId,
    sender: null, // System messages don't have a sender
    content: this.getSystemMessageContent(action, metadata),
    type: 'system',
    metadata: {
      action,
      ...metadata
    }
  });
};

// Static method to get system message content
messageSchema.statics.getSystemMessageContent = function(action, metadata) {
  const messages = {
    user_joined: `${metadata.userName} joined the trip`,
    user_left: `${metadata.userName} left the trip`,
    trip_updated: `Trip details were updated`,
    activity_added: `New activity "${metadata.activityTitle}" was added`,
    activity_voted: `${metadata.userName} voted on "${metadata.activityTitle}"`
  };
  
  return messages[action] || 'System notification';
};

module.exports = mongoose.model('Message', messageSchema);
