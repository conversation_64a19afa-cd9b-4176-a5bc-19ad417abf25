import { apiService } from './api';
import { User, LoginForm, RegisterForm, ApiResponse } from '../types';

interface AuthResponse {
  user: User;
  token: string;
}

export const authService = {
  /**
   * Login user with email and password
   */
  login: async (credentials: LoginForm): Promise<AuthResponse> => {
    const response = await apiService.post<{ user: User; token: string }>('/auth/login', credentials);
    
    // Store token and user in localStorage
    localStorage.setItem('token', response.token);
    localStorage.setItem('user', JSON.stringify(response.user));
    
    return response;
  },

  /**
   * Register new user
   */
  register: async (userData: RegisterForm): Promise<AuthResponse> => {
    const response = await apiService.post<{ user: User; token: string }>('/auth/register', userData);
    
    // Store token and user in localStorage
    localStorage.setItem('token', response.token);
    localStorage.setItem('user', JSON.stringify(response.user));
    
    return response;
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('currentTrip');
      localStorage.removeItem('preferences');
    }
  },

  /**
   * Get current user profile
   */
  getProfile: async (): Promise<User> => {
    return apiService.get<{ user: User }>('/auth/profile').then(response => response.user);
  },

  /**
   * Update user profile
   */
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    const response = await apiService.put<{ user: User }>('/auth/profile', userData);
    
    // Update user in localStorage
    localStorage.setItem('user', JSON.stringify(response.user));
    
    return response.user;
  },

  /**
   * Change password
   */
  changePassword: async (passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> => {
    await apiService.put('/auth/change-password', passwordData);
  },

  /**
   * Refresh JWT token
   */
  refreshToken: async (): Promise<string> => {
    const response = await apiService.post<{ token: string }>('/auth/refresh');
    
    // Update token in localStorage
    localStorage.setItem('token', response.token);
    
    return response.token;
  },

  /**
   * Verify if current token is valid
   */
  verifyToken: async (): Promise<User> => {
    return apiService.get<{ user: User }>('/auth/verify-token').then(response => response.user);
  },

  /**
   * Get stored user from localStorage
   */
  getStoredUser: (): User | null => {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing stored user:', error);
      return null;
    }
  },

  /**
   * Get stored token from localStorage
   */
  getStoredToken: (): string | null => {
    return localStorage.getItem('token');
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  /**
   * Clear all stored auth data
   */
  clearStoredAuth: (): void => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('currentTrip');
    localStorage.removeItem('preferences');
  },
};
