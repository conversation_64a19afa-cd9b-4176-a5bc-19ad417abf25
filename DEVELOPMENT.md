# Development Guide - Planit

This guide will help you set up and run the Planit collaborative trip planner application.

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)
```bash
# Run the setup script
node setup.js

# Start both servers
npm run dev
```

### Option 2: Manual Setup

1. **Install Dependencies**
   ```bash
   # Root dependencies (for development tools)
   npm install
   
   # Backend dependencies
   cd backend
   npm install
   
   # Frontend dependencies
   cd ../frontend
   npm install
   ```

2. **Environment Configuration**
   
   Create `backend/.env`:
   ```env
   MONGO_URI=mongodb://localhost:27017/planit_db
   JWT_SECRET=your_super_secret_jwt_key_here
   JWT_EXPIRES_IN=7d
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:5173
   ```
   
   Create `frontend/.env`:
   ```env
   VITE_API_BASE_URL=http://localhost:5000/api
   VITE_SOCKET_URL=http://localhost:5000
   VITE_APP_NAME=Planit
   ```

3. **Start Development Servers**
   ```bash
   # Start both servers concurrently
   npm run dev
   
   # Or start them separately:
   npm run dev:backend    # Backend on port 5000
   npm run dev:frontend   # Frontend on port 5173
   ```

## 📋 Prerequisites

- **Node.js**: Version 16 or higher
- **MongoDB**: Local installation or MongoDB Atlas account
- **npm**: Version 8 or higher

### Installing MongoDB Locally

**Windows:**
1. Download MongoDB Community Server from https://www.mongodb.com/try/download/community
2. Install and start the MongoDB service
3. MongoDB will run on `mongodb://localhost:27017`

**macOS:**
```bash
# Using Homebrew
brew tap mongodb/brew
brew install mongodb-community
brew services start mongodb/brew/mongodb-community
```

**Linux (Ubuntu):**
```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
```

## 🛠️ Development Commands

### Root Level Commands
```bash
npm run dev              # Start both backend and frontend
npm run dev:backend      # Start only backend server
npm run dev:frontend     # Start only frontend server
npm run build           # Build frontend for production
npm run test            # Run all tests
npm run install:all     # Install all dependencies
npm run clean           # Remove all node_modules
npm run reset           # Clean and reinstall everything
```

### Backend Commands
```bash
cd backend
npm run dev             # Start with nodemon (auto-restart)
npm start              # Start production server
npm test               # Run backend tests
npm run lint           # Run ESLint
```

### Frontend Commands
```bash
cd frontend
npm run dev            # Start development server
npm run build          # Build for production
npm run preview        # Preview production build
npm test               # Run frontend tests
npm run lint           # Run ESLint
npm run type-check     # TypeScript type checking
```

## 🔧 Configuration

### Backend Configuration (backend/.env)
```env
# Database
MONGO_URI=mongodb://localhost:27017/planit_db

# JWT Authentication
JWT_SECRET=your_super_secret_jwt_key_change_in_production
JWT_EXPIRES_IN=7d

# Server
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Email (Optional - for invitations)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Socket.io
SOCKET_CORS_ORIGIN=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Frontend Configuration (frontend/.env)
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000

# App Configuration
VITE_APP_NAME=Planit
VITE_APP_VERSION=1.0.0

# File Upload
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Optional: Google Maps API Key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Environment
VITE_NODE_ENV=development
```

## 🧪 Testing

### Running Tests
```bash
# All tests
npm test

# Backend tests only
cd backend && npm test

# Frontend tests only
cd frontend && npm test

# Watch mode
cd frontend && npm run test:watch
```

### Test Structure
- **Backend**: Uses Jest and Supertest for API testing
- **Frontend**: Uses Vitest and React Testing Library

## 🐛 Debugging

### Backend Debugging
1. **Check MongoDB Connection**:
   ```bash
   # Test MongoDB connection
   mongosh mongodb://localhost:27017/planit_db
   ```

2. **View Server Logs**:
   ```bash
   cd backend
   npm run dev
   # Check console output for errors
   ```

3. **API Testing**:
   ```bash
   # Test health endpoint
   curl http://localhost:5000/health
   ```

### Frontend Debugging
1. **Check Browser Console**: Open DevTools (F12) and check for errors
2. **Network Tab**: Monitor API requests and responses
3. **React DevTools**: Install React Developer Tools browser extension

### Common Issues

**Port Already in Use**:
```bash
# Kill process on port 5000
npx kill-port 5000

# Kill process on port 5173
npx kill-port 5173
```

**MongoDB Connection Issues**:
- Ensure MongoDB is running: `brew services list | grep mongodb` (macOS)
- Check connection string in `.env` file
- Verify database permissions

**CORS Issues**:
- Check `FRONTEND_URL` in backend `.env`
- Verify `VITE_API_BASE_URL` in frontend `.env`

## 📁 Project Structure

```
planit/
├── backend/                 # Express.js API server
│   ├── config/             # Database configuration
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Custom middleware
│   ├── models/            # Mongoose models
│   ├── routes/            # API routes
│   ├── utils/             # Helper utilities
│   ├── .env               # Environment variables
│   ├── package.json       # Backend dependencies
│   └── server.js          # Main server file
├── frontend/               # React application
│   ├── public/            # Static assets
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── context/       # React Context
│   │   ├── hooks/         # Custom hooks
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   ├── types/         # TypeScript types
│   │   └── utils/         # Utility functions
│   ├── .env               # Environment variables
│   └── package.json       # Frontend dependencies
├── .gitignore             # Git ignore rules
├── package.json           # Root package.json
├── setup.js               # Setup script
└── README.md              # Project documentation
```

## 🚀 Deployment

### Backend Deployment
1. **Environment Variables**: Set production values
2. **Process Manager**: Use PM2 for production
3. **Database**: Use MongoDB Atlas for cloud hosting
4. **Reverse Proxy**: Configure nginx for SSL and load balancing

### Frontend Deployment
1. **Build**: `npm run build`
2. **Static Hosting**: Deploy to Netlify, Vercel, or similar
3. **Environment**: Update API URLs for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Add tests for new functionality
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📞 Support

If you encounter any issues:
1. Check this development guide
2. Review the main README.md
3. Check existing GitHub issues
4. Create a new issue with detailed information

Happy coding! 🎉
