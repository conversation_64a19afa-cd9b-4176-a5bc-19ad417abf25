const express = require('express');
const { body } = require('express-validator');
const {
  getMessages,
  sendMessage,
  editMessage,
  deleteMessage,
  addReaction,
  removeReaction,
  getUnreadCount,
  markAllAsRead
} = require('../controllers/messageController');
const { authenticate, checkTripAccess } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const sendMessageValidation = [
  body('content')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message content must be between 1 and 1000 characters'),
  
  body('type')
    .optional()
    .isIn(['text', 'image', 'file', 'system', 'activity', 'poll'])
    .withMessage('Invalid message type'),
  
  body('replyTo')
    .optional()
    .isMongoId()
    .withMessage('Invalid reply message ID')
];

const editMessageValidation = [
  body('content')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message content must be between 1 and 1000 characters')
];

const reactionValidation = [
  body('emoji')
    .isLength({ min: 1, max: 10 })
    .withMessage('Emoji must be between 1 and 10 characters')
];

/**
 * @route   GET /api/trips/:tripId/messages
 * @desc    Get messages for a trip
 * @access  Private
 */
router.get('/trips/:tripId/messages', authenticate, checkTripAccess(), getMessages);

/**
 * @route   POST /api/trips/:tripId/messages
 * @desc    Send a message to a trip
 * @access  Private
 */
router.post('/trips/:tripId/messages', authenticate, checkTripAccess(), sendMessageValidation, sendMessage);

/**
 * @route   GET /api/trips/:tripId/messages/unread
 * @desc    Get unread message count for a trip
 * @access  Private
 */
router.get('/trips/:tripId/messages/unread', authenticate, checkTripAccess(), getUnreadCount);

/**
 * @route   POST /api/trips/:tripId/messages/mark-read
 * @desc    Mark all messages as read for a trip
 * @access  Private
 */
router.post('/trips/:tripId/messages/mark-read', authenticate, checkTripAccess(), markAllAsRead);

/**
 * @route   PUT /api/messages/:id
 * @desc    Edit a message
 * @access  Private
 */
router.put('/:id', authenticate, editMessageValidation, editMessage);

/**
 * @route   DELETE /api/messages/:id
 * @desc    Delete a message
 * @access  Private
 */
router.delete('/:id', authenticate, deleteMessage);

/**
 * @route   POST /api/messages/:id/react
 * @desc    Add reaction to a message
 * @access  Private
 */
router.post('/:id/react', authenticate, reactionValidation, addReaction);

/**
 * @route   DELETE /api/messages/:id/react
 * @desc    Remove reaction from a message
 * @access  Private
 */
router.delete('/:id/react', authenticate, reactionValidation, removeReaction);

module.exports = router;
