import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '../types';

// Create axios instance with base configuration
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    
    // Return a standardized error format
    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
    return Promise.reject({
      message: errorMessage,
      status: error.response?.status,
      data: error.response?.data,
    });
  }
);

// Generic API response handler
const handleResponse = <T>(response: AxiosResponse<ApiResponse<T>>): T => {
  if (response.data.success) {
    return response.data.data as T;
  }
  throw new Error(response.data.message || 'API request failed');
};

// API service methods
export const apiService = {
  // Generic methods
  get: <T>(url: string, params?: any): Promise<T> =>
    api.get(url, { params }).then(handleResponse),
  
  post: <T>(url: string, data?: any): Promise<T> =>
    api.post(url, data).then(handleResponse),
  
  put: <T>(url: string, data?: any): Promise<T> =>
    api.put(url, data).then(handleResponse),
  
  delete: <T>(url: string): Promise<T> =>
    api.delete(url).then(handleResponse),
  
  // File upload
  upload: <T>(url: string, formData: FormData): Promise<T> =>
    api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(handleResponse),
};

// Export the axios instance for direct use if needed
export default api;
