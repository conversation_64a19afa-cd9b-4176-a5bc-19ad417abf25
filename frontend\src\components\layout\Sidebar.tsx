import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  MapPin, 
  Users, 
  Calendar, 
  MessageCircle, 
  Settings,
  Plus
} from 'lucide-react';
import { cn } from '../../utils';
import Button from '../ui/Button';

const Sidebar: React.FC = () => {
  const location = useLocation();

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'My Trips', href: '/trips', icon: MapPin },
    { name: 'Friends\' Trips', href: '/friends-trips', icon: Users },
    { name: 'Calendar', href: '/calendar', icon: Calendar },
    { name: 'Messages', href: '/messages', icon: MessageCircle },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 min-h-screen">
      <div className="p-6">
        {/* Create New Trip Button */}
        <Link to="/trips/create">
          <Button className="w-full mb-6">
            <Plus className="h-4 w-4 mr-2" />
            Create New Trip
          </Button>
        </Link>

        {/* Navigation */}
        <nav className="space-y-1">
          {navigation.map((item) => {
            const isActive = location.pathname === item.href || 
                           (item.href !== '/dashboard' && location.pathname.startsWith(item.href));
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                )}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </Link>
            );
          })}
        </nav>

        {/* Recent Trips */}
        <div className="mt-8">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
            Recent Trips
          </h3>
          <div className="space-y-1">
            {/* This would be populated with actual recent trips */}
            <div className="text-sm text-gray-500 px-3 py-2">
              No recent trips
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
