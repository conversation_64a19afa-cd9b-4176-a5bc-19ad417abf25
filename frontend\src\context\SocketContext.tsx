import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { Message, Activity, User, MessageType } from '../types';
import toast from 'react-hot-toast';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  joinTrip: (tripId: string) => void;
  leaveTrip: (tripId: string) => void;
  sendMessage: (tripId: string, content: string, type?: MessageType) => void;
  voteActivity: (activityId: string, vote: 'up' | 'down' | 'remove') => void;
  onNewMessage: (callback: (message: Message) => void) => void;
  onActivityVoteUpdate: (callback: (data: { activity: Activity; voter: User; vote: string }) => void) => void;
  onTripUpdate: (callback: (data: any) => void) => void;
  onUserJoinedTrip: (callback: (data: { user: User; tripId: string }) => void) => void;
  onUserLeftTrip: (callback: (data: { user: User; tripId: string }) => void) => void;
  onUserTyping: (callback: (data: { user: User; tripId: string }) => void) => void;
  onUserStoppedTyping: (callback: (data: { user: User; tripId: string }) => void) => void;
  startTyping: (tripId: string) => void;
  stopTyping: (tripId: string) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user, token, isAuthenticated } = useAuth();

  // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && token && user) {
      const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000';
      
      const newSocket = io(socketUrl, {
        auth: {
          token: token,
        },
        transports: ['websocket', 'polling'],
      });

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('Socket connected:', newSocket.id);
        setIsConnected(true);
      });

      newSocket.on('disconnect', () => {
        console.log('Socket disconnected');
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setIsConnected(false);
        toast.error('Connection error. Some features may not work properly.');
      });

      // Error handler
      newSocket.on('error', (error) => {
        console.error('Socket error:', error);
        toast.error(error.message || 'Socket error occurred');
      });

      setSocket(newSocket);

      // Cleanup on unmount or auth change
      return () => {
        newSocket.close();
        setSocket(null);
        setIsConnected(false);
      };
    } else {
      // Disconnect socket if not authenticated
      if (socket) {
        socket.close();
        setSocket(null);
        setIsConnected(false);
      }
    }
  }, [isAuthenticated, token, user]);

  // Socket methods
  const joinTrip = (tripId: string) => {
    if (socket && isConnected) {
      socket.emit('join-trip', tripId);
    }
  };

  const leaveTrip = (tripId: string) => {
    if (socket && isConnected) {
      socket.emit('leave-trip', tripId);
    }
  };

  const sendMessage = (tripId: string, content: string, type: MessageType = 'text') => {
    if (socket && isConnected) {
      socket.emit('send-message', {
        tripId,
        content,
        type,
      });
    }
  };

  const voteActivity = (activityId: string, vote: 'up' | 'down' | 'remove') => {
    if (socket && isConnected) {
      socket.emit('activity-vote', {
        activityId,
        vote,
      });
    }
  };

  const startTyping = (tripId: string) => {
    if (socket && isConnected) {
      socket.emit('typing-start', tripId);
    }
  };

  const stopTyping = (tripId: string) => {
    if (socket && isConnected) {
      socket.emit('typing-stop', tripId);
    }
  };

  // Event listeners
  const onNewMessage = (callback: (message: Message) => void) => {
    if (socket) {
      socket.on('new-message', callback);
      return () => socket.off('new-message', callback);
    }
  };

  const onActivityVoteUpdate = (callback: (data: { activity: Activity; voter: User; vote: string }) => void) => {
    if (socket) {
      socket.on('activity-vote-update', callback);
      return () => socket.off('activity-vote-update', callback);
    }
  };

  const onTripUpdate = (callback: (data: any) => void) => {
    if (socket) {
      socket.on('trip-updated', callback);
      return () => socket.off('trip-updated', callback);
    }
  };

  const onUserJoinedTrip = (callback: (data: { user: User; tripId: string }) => void) => {
    if (socket) {
      socket.on('user-joined-trip', callback);
      return () => socket.off('user-joined-trip', callback);
    }
  };

  const onUserLeftTrip = (callback: (data: { user: User; tripId: string }) => void) => {
    if (socket) {
      socket.on('user-left-trip', callback);
      return () => socket.off('user-left-trip', callback);
    }
  };

  const onUserTyping = (callback: (data: { user: User; tripId: string }) => void) => {
    if (socket) {
      socket.on('user-typing', callback);
      return () => socket.off('user-typing', callback);
    }
  };

  const onUserStoppedTyping = (callback: (data: { user: User; tripId: string }) => void) => {
    if (socket) {
      socket.on('user-stopped-typing', callback);
      return () => socket.off('user-stopped-typing', callback);
    }
  };

  const value: SocketContextType = {
    socket,
    isConnected,
    joinTrip,
    leaveTrip,
    sendMessage,
    voteActivity,
    onNewMessage,
    onActivityVoteUpdate,
    onTripUpdate,
    onUserJoinedTrip,
    onUserLeftTrip,
    onUserTyping,
    onUserStoppedTyping,
    startTyping,
    stopTyping,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

// Custom hook to use socket context
export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default SocketContext;
