# Planit Backend - Collaborative Trip Planner API

This is the backend API server for the Planit collaborative trip planning application. It provides RESTful APIs and real-time communication features using Socket.io.

## Features

- User authentication (JWT-based)
- Trip management and collaboration
- Real-time activity voting
- Group chat functionality
- File upload support
- Email invitations
- Budget tracking

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Real-time**: Socket.io
- **Email**: Nodemailer
- **Security**: Helmet, CORS, Rate limiting

## Prerequisites

- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn package manager

## Installation

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file with your configuration values.

## Environment Variables

Create a `.env` file in the backend root directory with the following variables:

```env
# Database
MONGO_URI=mongodb://localhost:27017/planit_db

# JWT
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# Server
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Email (optional, for invitations)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

## Running the Application

### Development Mode
```bash
npm run dev
```
This starts the server with nodemon for automatic restarts on file changes.

### Production Mode
```bash
npm start
```

The server will start on the port specified in your `.env` file (default: 5000).

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile (protected)

### Trips
- `GET /api/trips` - Get user's trips
- `POST /api/trips` - Create a new trip
- `GET /api/trips/:id` - Get trip details
- `PUT /api/trips/:id` - Update trip
- `DELETE /api/trips/:id` - Delete trip
- `POST /api/trips/:id/invite` - Invite users to trip

### Activities
- `GET /api/trips/:tripId/activities` - Get trip activities
- `POST /api/trips/:tripId/activities` - Add activity
- `PUT /api/activities/:id` - Update activity
- `DELETE /api/activities/:id` - Delete activity
- `POST /api/activities/:id/vote` - Vote on activity

### Messages
- `GET /api/trips/:tripId/messages` - Get trip messages
- `POST /api/trips/:tripId/messages` - Send message

## Database Schema

The application uses MongoDB with the following main collections:
- `users` - User accounts and profiles
- `trips` - Trip information and settings
- `activities` - Trip activities and votes
- `messages` - Chat messages for trips

## Socket.io Events

Real-time events for collaborative features:
- `join-trip` - Join a trip room
- `activity-vote` - Real-time activity voting
- `new-message` - Chat messages
- `trip-update` - Trip information updates

## Testing

Run the test suite:
```bash
npm test
```

## Production Deployment

1. Set `NODE_ENV=production` in your environment
2. Use a process manager like PM2:
   ```bash
   npm install -g pm2
   pm2 start server.js --name planit-backend
   ```
3. Set up a reverse proxy (nginx) for SSL and load balancing
4. Use MongoDB Atlas for database hosting

## Security Features

- JWT token authentication
- Password hashing with bcrypt
- CORS protection
- Rate limiting
- Input validation
- Helmet security headers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## License

MIT License - see LICENSE file for details
