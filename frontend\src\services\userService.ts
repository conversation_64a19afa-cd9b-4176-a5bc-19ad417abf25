import { apiService } from './api';
import { User } from '../types';

export const userService = {
  /**
   * Search users by email or name
   */
  searchUsers: async (query: string, limit?: number): Promise<User[]> => {
    return apiService.get<{ users: User[] }>('/users/search', { q: query, limit }).then(response => response.users);
  },

  /**
   * Get user profile by ID
   */
  getUserById: async (userId: string): Promise<User> => {
    return apiService.get<{ user: User }>(`/users/${userId}`).then(response => response.user);
  },
};
