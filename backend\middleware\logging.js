const colors = require('colors');
const mongoose = require('mongoose');

/**
 * Enhanced logging middleware for comprehensive request tracking
 * Shows all page transitions, MongoDB status, and network errors
 */

// Configure colors theme
colors.setTheme({
  info: 'cyan',
  warn: 'yellow',
  error: 'red',
  success: 'green',
  debug: 'blue',
  verbose: 'magenta'
});

/**
 * Get MongoDB connection status with colored output
 */
const getDBStatus = () => {
  const dbState = mongoose.connection.readyState;
  switch (dbState) {
    case 0:
      return '🔴 Disconnected'.error;
    case 1:
      return '🟢 Connected'.success;
    case 2:
      return '🟡 Connecting'.warn;
    case 3:
      return '🟠 Disconnecting'.warn;
    default:
      return '⚪ Unknown'.debug;
  }
};

/**
 * Get network status indicator
 */
const getNetworkStatus = (req) => {
  const userAgent = req.get('User-Agent') || 'Unknown';
  const isOnline = req.get('Connection') !== 'close';
  return {
    status: isOnline ? '🌐 Online'.success : '📡 Offline'.error,
    userAgent: userAgent.substring(0, 50) + (userAgent.length > 50 ? '...' : ''),
    ip: req.ip || req.connection.remoteAddress || 'Unknown'
  };
};

/**
 * Enhanced request logging middleware
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  const networkInfo = getNetworkStatus(req);
  
  // Log incoming request with full details
  console.log('\n' + '='.repeat(80).info);
  console.log(`📡 INCOMING REQUEST`.info.bold);
  console.log(`⏰ Time: ${timestamp}`.verbose);
  console.log(`🔗 Method: ${req.method}`.info + ` | 🎯 URL: ${req.originalUrl}`.info);
  console.log(`🌐 IP: ${networkInfo.ip}`.debug + ` | ${networkInfo.status}`);
  console.log(`🗄️  Database: ${getDBStatus()}`);
  
  // Log request headers for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log(`📋 Headers:`.debug);
    Object.keys(req.headers).forEach(key => {
      if (['authorization', 'cookie'].includes(key.toLowerCase())) {
        console.log(`   ${key}: ${'[HIDDEN]'.warn}`);
      } else {
        console.log(`   ${key}: ${req.headers[key]}`.debug);
      }
    });
  }
  
  // Log request body (excluding sensitive data)
  if (req.body && Object.keys(req.body).length > 0) {
    const sanitizedBody = { ...req.body };
    if (sanitizedBody.password) sanitizedBody.password = '[HIDDEN]';
    if (sanitizedBody.token) sanitizedBody.token = '[HIDDEN]';
    console.log(`📦 Body:`.debug, JSON.stringify(sanitizedBody, null, 2).debug);
  }
  
  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(data) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`📤 RESPONSE`.success.bold);
    console.log(`⚡ Status: ${res.statusCode}`.success + ` | ⏱️  Duration: ${duration}ms`.verbose);
    console.log(`🗄️  Database: ${getDBStatus()}`);
    
    // Log response data (excluding sensitive information)
    if (data) {
      const sanitizedData = JSON.stringify(data, (key, value) => {
        if (['password', 'token', 'secret'].includes(key.toLowerCase())) {
          return '[HIDDEN]';
        }
        return value;
      }, 2);
      console.log(`📋 Response Data:`.success, sanitizedData.success);
    }
    
    console.log('='.repeat(80).info + '\n');
    
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Page transition tracker
 */
const pageTransitionLogger = (req, res, next) => {
  const referer = req.get('Referer');
  const currentUrl = req.originalUrl;
  
  if (referer && referer !== req.get('Host')) {
    console.log(`🔄 PAGE TRANSITION`.verbose.bold);
    console.log(`   From: ${referer}`.verbose);
    console.log(`   To: ${currentUrl}`.verbose);
    console.log(`   User: ${req.user ? req.user.email : 'Anonymous'}`.verbose);
  }
  
  next();
};

/**
 * Network error detector and handler
 */
const networkErrorHandler = (err, req, res, next) => {
  const timestamp = new Date().toISOString();
  
  console.log('\n' + '❌'.repeat(40).error);
  console.log(`🚨 NETWORK ERROR DETECTED`.error.bold);
  console.log(`⏰ Time: ${timestamp}`.error);
  console.log(`🔗 Request: ${req.method} ${req.originalUrl}`.error);
  console.log(`🌐 Client IP: ${req.ip}`.error);
  console.log(`🗄️  Database: ${getDBStatus()}`);
  
  // Detect specific network error types
  if (err.code === 'ECONNREFUSED') {
    console.log(`🔌 Connection Refused - Service may be down`.error);
    console.log(`💡 Solution: Check if the target service is running`.warn);
  } else if (err.code === 'ENOTFOUND') {
    console.log(`🔍 DNS Resolution Failed - Host not found`.error);
    console.log(`💡 Solution: Check network connectivity and DNS settings`.warn);
  } else if (err.code === 'ETIMEDOUT') {
    console.log(`⏰ Connection Timeout - Request took too long`.error);
    console.log(`💡 Solution: Check network speed and server response time`.warn);
  } else if (err.name === 'MongoNetworkError') {
    console.log(`🗄️  MongoDB Network Error - Database connection issue`.error);
    console.log(`💡 Solution: Check MongoDB connection string and network access`.warn);
  } else if (err.name === 'MongoTimeoutError') {
    console.log(`⏰ MongoDB Timeout - Database operation took too long`.error);
    console.log(`💡 Solution: Check database performance and query optimization`.warn);
  }
  
  console.log(`📋 Error Details:`.error);
  console.log(`   Name: ${err.name}`.error);
  console.log(`   Message: ${err.message}`.error);
  console.log(`   Code: ${err.code || 'N/A'}`.error);
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`📚 Stack Trace:`.error);
    console.log(err.stack.error);
  }
  
  console.log('❌'.repeat(40).error + '\n');
  
  next(err);
};

/**
 * MongoDB connection event listeners for real-time status
 */
const setupDatabaseMonitoring = () => {
  mongoose.connection.on('connected', () => {
    console.log(`✅ MongoDB Connected Successfully!`.success.bold);
    console.log(`📦 Host: ${mongoose.connection.host}`.success);
    console.log(`🗄️  Database: ${mongoose.connection.name}`.success);
  });
  
  mongoose.connection.on('error', (err) => {
    console.log(`❌ MongoDB Connection Error:`.error.bold);
    console.log(`   ${err.message}`.error);
  });
  
  mongoose.connection.on('disconnected', () => {
    console.log(`🔴 MongoDB Disconnected`.error.bold);
    console.log(`💡 Attempting to reconnect...`.warn);
  });
  
  mongoose.connection.on('reconnected', () => {
    console.log(`🔄 MongoDB Reconnected Successfully!`.success.bold);
  });
};

module.exports = {
  requestLogger,
  pageTransitionLogger,
  networkErrorHandler,
  setupDatabaseMonitoring,
  getDBStatus
};
