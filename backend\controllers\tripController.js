const { validationResult } = require('express-validator');
const Trip = require('../models/Trip');
const User = require('../models/User');
const Activity = require('../models/Activity');
const Message = require('../models/Message');

/**
 * Get all trips for the authenticated user
 * GET /api/trips
 */
const getTrips = async (req, res) => {
  try {
    const userId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;
    
    const skip = (page - 1) * limit;
    const query = {
      $or: [
        { creator: userId },
        { 'participants.user': userId, 'participants.status': 'joined' }
      ]
    };
    
    if (status) {
      query.status = status;
    }
    
    const trips = await Trip.find(query)
      .populate('creator', 'name email profilePhoto')
      .populate('participants.user', 'name email profilePhoto')
      .populate('activities', 'title date category status')
      .sort({ lastActivity: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Trip.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        trips,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
    
  } catch (error) {
    console.error('Get trips error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching trips'
    });
  }
};

/**
 * Get a specific trip by ID
 * GET /api/trips/:id
 */
const getTripById = async (req, res) => {
  try {
    const tripId = req.params.id;
    const userId = req.user._id;
    
    const trip = await Trip.findById(tripId)
      .populate('creator', 'name email profilePhoto')
      .populate('participants.user', 'name email profilePhoto')
      .populate({
        path: 'activities',
        populate: {
          path: 'creator votes.user',
          select: 'name email profilePhoto'
        }
      });
    
    if (!trip) {
      return res.status(404).json({
        success: false,
        message: 'Trip not found'
      });
    }
    
    // Check if user has access to this trip
    const isCreator = trip.creator._id.toString() === userId.toString();
    const isParticipant = trip.participants.some(p => 
      p.user._id.toString() === userId.toString() && p.status === 'joined'
    );
    
    if (!isCreator && !isParticipant) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not a participant of this trip.'
      });
    }
    
    res.json({
      success: true,
      data: {
        trip
      }
    });
    
  } catch (error) {
    console.error('Get trip by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching trip'
    });
  }
};

/**
 * Create a new trip
 * POST /api/trips
 */
const createTrip = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const {
      name,
      description,
      destination,
      startDate,
      endDate,
      budget,
      settings,
      tags
    } = req.body;
    
    const userId = req.user._id;
    
    // Create trip
    const trip = new Trip({
      name: name.trim(),
      description: description?.trim() || '',
      destination: destination?.trim() || '',
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      budget: budget || { total: 0, currency: 'USD', spent: 0 },
      creator: userId,
      participants: [{
        user: userId,
        role: 'creator',
        status: 'joined',
        permissions: {
          canEdit: true,
          canInvite: true,
          canDelete: true
        }
      }],
      settings: settings || {},
      tags: tags || []
    });
    
    await trip.save();
    
    // Add trip to user's trips array
    await User.findByIdAndUpdate(userId, {
      $push: { trips: trip._id }
    });
    
    // Populate the trip data
    const populatedTrip = await Trip.findById(trip._id)
      .populate('creator', 'name email profilePhoto')
      .populate('participants.user', 'name email profilePhoto');
    
    res.status(201).json({
      success: true,
      message: 'Trip created successfully',
      data: {
        trip: populatedTrip
      }
    });
    
  } catch (error) {
    console.error('Create trip error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating trip'
    });
  }
};

/**
 * Update a trip
 * PUT /api/trips/:id
 */
const updateTrip = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const tripId = req.params.id;
    const updateData = req.body;
    
    // Remove fields that shouldn't be updated directly
    delete updateData.creator;
    delete updateData.participants;
    delete updateData.tripCode;
    delete updateData.activities;
    
    const trip = await Trip.findByIdAndUpdate(
      tripId,
      { ...updateData, lastActivity: new Date() },
      { new: true, runValidators: true }
    )
      .populate('creator', 'name email profilePhoto')
      .populate('participants.user', 'name email profilePhoto');
    
    if (!trip) {
      return res.status(404).json({
        success: false,
        message: 'Trip not found'
      });
    }
    
    // Create system message for trip update
    await Message.createSystemMessage(tripId, 'trip_updated', {
      userName: req.user.name
    });
    
    res.json({
      success: true,
      message: 'Trip updated successfully',
      data: {
        trip
      }
    });
    
  } catch (error) {
    console.error('Update trip error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating trip'
    });
  }
};

/**
 * Delete a trip
 * DELETE /api/trips/:id
 */
const deleteTrip = async (req, res) => {
  try {
    const tripId = req.params.id;
    
    // Delete associated activities and messages
    await Activity.deleteMany({ trip: tripId });
    await Message.deleteMany({ trip: tripId });
    
    // Remove trip from all users' trips arrays
    await User.updateMany(
      { trips: tripId },
      { $pull: { trips: tripId } }
    );
    
    // Delete the trip
    const trip = await Trip.findByIdAndDelete(tripId);
    
    if (!trip) {
      return res.status(404).json({
        success: false,
        message: 'Trip not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Trip deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete trip error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting trip'
    });
  }
};

/**
 * Join a trip using trip code
 * POST /api/trips/join
 */
const joinTrip = async (req, res) => {
  try {
    const { tripCode } = req.body;
    const userId = req.user._id;
    
    if (!tripCode) {
      return res.status(400).json({
        success: false,
        message: 'Trip code is required'
      });
    }
    
    const trip = await Trip.findByCode(tripCode);
    
    if (!trip) {
      return res.status(404).json({
        success: false,
        message: 'Invalid trip code'
      });
    }
    
    // Check if user is already a participant
    const existingParticipant = trip.participants.find(p => 
      p.user.toString() === userId.toString()
    );
    
    if (existingParticipant) {
      if (existingParticipant.status === 'joined') {
        return res.status(400).json({
          success: false,
          message: 'You are already a member of this trip'
        });
      } else {
        // Update status to joined
        existingParticipant.status = 'joined';
        existingParticipant.joinedAt = new Date();
      }
    } else {
      // Add new participant
      trip.participants.push({
        user: userId,
        role: 'member',
        status: 'joined',
        joinedAt: new Date()
      });
    }
    
    trip.lastActivity = new Date();
    await trip.save();
    
    // Add trip to user's trips array
    await User.findByIdAndUpdate(userId, {
      $addToSet: { trips: trip._id }
    });
    
    // Create system message
    await Message.createSystemMessage(trip._id, 'user_joined', {
      userName: req.user.name
    });
    
    const populatedTrip = await Trip.findById(trip._id)
      .populate('creator', 'name email profilePhoto')
      .populate('participants.user', 'name email profilePhoto');
    
    res.json({
      success: true,
      message: 'Successfully joined the trip',
      data: {
        trip: populatedTrip
      }
    });
    
  } catch (error) {
    console.error('Join trip error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while joining trip'
    });
  }
};

module.exports = {
  getTrips,
  getTripById,
  createTrip,
  updateTrip,
  deleteTrip,
  joinTrip
};
