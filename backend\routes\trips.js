const express = require('express');
const { body } = require('express-validator');
const {
  getTrips,
  getTripById,
  createTrip,
  updateTrip,
  deleteTrip,
  joinTrip
} = require('../controllers/tripController');
const { authenticate, checkTripAccess, checkEditPermission } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createTripValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Trip name must be between 1 and 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('destination')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Destination cannot exceed 100 characters'),
  
  body('startDate')
    .isISO8601()
    .withMessage('Start date must be a valid date')
    .custom((value) => {
      if (new Date(value) < new Date()) {
        throw new Error('Start date cannot be in the past');
      }
      return true;
    }),
  
  body('endDate')
    .isISO8601()
    .withMessage('End date must be a valid date')
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  body('budget.total')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number'),
  
  body('budget.currency')
    .optional()
    .isIn(['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'])
    .withMessage('Invalid currency')
];

const updateTripValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Trip name must be between 1 and 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('destination')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Destination cannot exceed 100 characters'),
  
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
  
  body('budget.total')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number'),
  
  body('status')
    .optional()
    .isIn(['planning', 'active', 'completed', 'cancelled'])
    .withMessage('Invalid trip status')
];

const joinTripValidation = [
  body('tripCode')
    .trim()
    .isLength({ min: 8, max: 8 })
    .withMessage('Trip code must be 8 characters long')
    .isAlphanumeric()
    .withMessage('Trip code must contain only letters and numbers')
];

/**
 * @route   GET /api/trips
 * @desc    Get all trips for authenticated user
 * @access  Private
 */
router.get('/', authenticate, getTrips);

/**
 * @route   GET /api/trips/:id
 * @desc    Get trip by ID
 * @access  Private
 */
router.get('/:id', authenticate, checkTripAccess(), getTripById);

/**
 * @route   POST /api/trips
 * @desc    Create a new trip
 * @access  Private
 */
router.post('/', authenticate, createTripValidation, createTrip);

/**
 * @route   PUT /api/trips/:id
 * @desc    Update a trip
 * @access  Private
 */
router.put('/:id', authenticate, checkTripAccess(), checkEditPermission, updateTripValidation, updateTrip);

/**
 * @route   DELETE /api/trips/:id
 * @desc    Delete a trip
 * @access  Private (Creator only)
 */
router.delete('/:id', authenticate, checkTripAccess(), (req, res, next) => {
  // Only creator can delete trip
  if (!req.isCreator) {
    return res.status(403).json({
      success: false,
      message: 'Only the trip creator can delete this trip'
    });
  }
  next();
}, deleteTrip);

/**
 * @route   POST /api/trips/join
 * @desc    Join a trip using trip code
 * @access  Private
 */
router.post('/join', authenticate, joinTripValidation, joinTrip);

module.exports = router;
