const colors = require('colors');
const mongoose = require('mongoose');

/**
 * Enhanced Network Error Handler for Account Creation and General Operations
 * Provides detailed error analysis and user-friendly solutions
 */

// Configure colors
colors.setTheme({
  info: 'cyan',
  warn: 'yellow',
  error: 'red',
  success: 'green',
  debug: 'blue',
  verbose: 'magenta'
});

/**
 * Network connectivity checker
 */
const checkNetworkConnectivity = async () => {
  try {
    const dns = require('dns').promises;
    await dns.lookup('google.com');
    return { connected: true, message: 'Internet connection available' };
  } catch (error) {
    return { connected: false, message: 'No internet connection detected' };
  }
};

/**
 * MongoDB connection health checker
 */
const checkDatabaseHealth = async () => {
  try {
    const dbState = mongoose.connection.readyState;
    const isConnected = dbState === 1;
    
    if (isConnected) {
      // Test database operation
      await mongoose.connection.db.admin().ping();
      return { 
        healthy: true, 
        message: 'Database connection is healthy',
        state: 'Connected'
      };
    } else {
      return { 
        healthy: false, 
        message: 'Database not connected',
        state: dbState === 0 ? 'Disconnected' : dbState === 2 ? 'Connecting' : 'Unknown'
      };
    }
  } catch (error) {
    return { 
      healthy: false, 
      message: `Database health check failed: ${error.message}`,
      state: 'Error'
    };
  }
};

/**
 * Enhanced error handler for account creation
 */
const accountCreationErrorHandler = async (error, req, res, next) => {
  const timestamp = new Date().toISOString();
  const userEmail = req.body?.email || 'Unknown';
  
  console.log('\n' + '🚨'.repeat(50).error);
  console.log(`🔥 ACCOUNT CREATION ERROR`.error.bold);
  console.log(`⏰ Time: ${timestamp}`.error);
  console.log(`👤 User Email: ${userEmail}`.error);
  console.log(`🌐 Client IP: ${req.ip}`.error);
  
  // Check network and database connectivity
  const networkStatus = await checkNetworkConnectivity();
  const dbHealth = await checkDatabaseHealth();
  
  console.log(`🌐 Network Status: ${networkStatus.connected ? '✅ Connected'.success : '❌ Disconnected'.error}`);
  console.log(`🗄️  Database Status: ${dbHealth.healthy ? '✅ Healthy'.success : '❌ Unhealthy'.error} (${dbHealth.state})`);
  
  let errorResponse = {
    success: false,
    message: 'Account creation failed',
    timestamp,
    errorCode: 'UNKNOWN_ERROR'
  };
  
  // Handle specific error types
  if (error.name === 'ValidationError') {
    console.log(`📋 Validation Error Detected`.warn.bold);
    const validationErrors = Object.values(error.errors).map(e => e.message);
    console.log(`   Errors: ${validationErrors.join(', ')}`.warn);
    
    errorResponse = {
      success: false,
      message: 'Please check your input data',
      errors: validationErrors,
      errorCode: 'VALIDATION_ERROR',
      timestamp
    };
    
    return res.status(400).json(errorResponse);
  }
  
  if (error.code === 11000) {
    console.log(`🔄 Duplicate Key Error - User Already Exists`.warn.bold);
    const field = Object.keys(error.keyValue)[0];
    console.log(`   Duplicate Field: ${field}`.warn);
    
    errorResponse = {
      success: false,
      message: `An account with this ${field} already exists`,
      errorCode: 'DUPLICATE_ACCOUNT',
      timestamp
    };
    
    return res.status(400).json(errorResponse);
  }
  
  if (error.name === 'MongoNetworkError' || error.name === 'MongoTimeoutError') {
    console.log(`🗄️  MongoDB Connection Issue`.error.bold);
    console.log(`   Error: ${error.message}`.error);
    console.log(`   💡 Attempting database reconnection...`.warn);
    
    // Attempt to reconnect
    try {
      await mongoose.connection.close();
      await mongoose.connect(process.env.MONGO_URI);
      console.log(`✅ Database reconnection successful`.success);
    } catch (reconnectError) {
      console.log(`❌ Database reconnection failed: ${reconnectError.message}`.error);
    }
    
    errorResponse = {
      success: false,
      message: 'Database connection issue. Please try again in a moment.',
      errorCode: 'DATABASE_CONNECTION_ERROR',
      timestamp,
      retryAfter: 5000 // Suggest retry after 5 seconds
    };
    
    return res.status(503).json(errorResponse);
  }
  
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
    console.log(`🌐 Network Connectivity Issue`.error.bold);
    console.log(`   Error Code: ${error.code}`.error);
    console.log(`   Message: ${error.message}`.error);
    
    if (!networkStatus.connected) {
      console.log(`   💡 No internet connection detected`.warn);
      errorResponse = {
        success: false,
        message: 'Network connection issue. Please check your internet connection.',
        errorCode: 'NETWORK_ERROR',
        timestamp
      };
    } else {
      console.log(`   💡 Service connectivity issue`.warn);
      errorResponse = {
        success: false,
        message: 'Service temporarily unavailable. Please try again later.',
        errorCode: 'SERVICE_UNAVAILABLE',
        timestamp,
        retryAfter: 10000
      };
    }
    
    return res.status(503).json(errorResponse);
  }
  
  if (error.name === 'CastError') {
    console.log(`🔄 Data Type Error`.warn.bold);
    console.log(`   Invalid data type for field: ${error.path}`.warn);
    
    errorResponse = {
      success: false,
      message: 'Invalid data format provided',
      errorCode: 'INVALID_DATA_FORMAT',
      timestamp
    };
    
    return res.status(400).json(errorResponse);
  }
  
  // Handle JWT errors
  if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    console.log(`🔐 Authentication Error`.warn.bold);
    console.log(`   JWT Error: ${error.message}`.warn);
    
    errorResponse = {
      success: false,
      message: 'Authentication failed. Please login again.',
      errorCode: 'AUTH_ERROR',
      timestamp
    };
    
    return res.status(401).json(errorResponse);
  }
  
  // Generic error handling
  console.log(`❓ Unknown Error Type`.error.bold);
  console.log(`   Name: ${error.name}`.error);
  console.log(`   Message: ${error.message}`.error);
  console.log(`   Code: ${error.code || 'N/A'}`.error);
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`📚 Stack Trace:`.debug);
    console.log(error.stack.debug);
  }
  
  // Log error to file or external service in production
  if (process.env.NODE_ENV === 'production') {
    // TODO: Implement error logging to external service
    console.log(`📝 Error logged for investigation`.info);
  }
  
  console.log('🚨'.repeat(50).error + '\n');
  
  errorResponse = {
    success: false,
    message: 'An unexpected error occurred during account creation. Please try again.',
    errorCode: 'INTERNAL_SERVER_ERROR',
    timestamp
  };
  
  res.status(500).json(errorResponse);
};

/**
 * General network error middleware
 */
const generalNetworkErrorHandler = async (error, req, res, next) => {
  // Skip if response already sent
  if (res.headersSent) {
    return next(error);
  }
  
  // Check if this is an account creation request
  if (req.originalUrl.includes('/auth/register')) {
    return accountCreationErrorHandler(error, req, res, next);
  }
  
  // Handle other network errors
  const timestamp = new Date().toISOString();
  
  console.log('\n' + '⚠️'.repeat(40).warn);
  console.log(`🌐 NETWORK ERROR`.warn.bold);
  console.log(`⏰ Time: ${timestamp}`.warn);
  console.log(`🔗 Request: ${req.method} ${req.originalUrl}`.warn);
  console.log(`🌐 Client IP: ${req.ip}`.warn);
  console.log(`📋 Error: ${error.message}`.warn);
  console.log('⚠️'.repeat(40).warn + '\n');
  
  // Pass to next error handler
  next(error);
};

module.exports = {
  accountCreationErrorHandler,
  generalNetworkErrorHandler,
  checkNetworkConnectivity,
  checkDatabaseHealth
};
