const express = require('express');
const { body } = require('express-validator');
const {
  getActivities,
  createActivity,
  updateActivity,
  deleteActivity,
  voteActivity,
  getActivityStats
} = require('../controllers/activityController');
const { authenticate, checkTripAccess } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createActivityValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Activity title must be between 1 and 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('date')
    .isISO8601()
    .withMessage('Date must be a valid date'),
  
  body('time.start')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Start time must be in HH:MM format'),
  
  body('time.end')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('End time must be in HH:MM format'),
  
  body('category')
    .isIn([
      'accommodation', 'transportation', 'food', 'entertainment',
      'sightseeing', 'shopping', 'outdoor', 'culture', 'nightlife',
      'relaxation', 'business', 'other'
    ])
    .withMessage('Invalid activity category'),
  
  body('cost.amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Cost amount must be a positive number'),
  
  body('cost.currency')
    .optional()
    .isIn(['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'])
    .withMessage('Invalid currency'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'must-do'])
    .withMessage('Invalid priority level'),
  
  body('location.coordinates.latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  
  body('location.coordinates.longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180')
];

const updateActivityValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Activity title must be between 1 and 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('date')
    .optional()
    .isISO8601()
    .withMessage('Date must be a valid date'),
  
  body('category')
    .optional()
    .isIn([
      'accommodation', 'transportation', 'food', 'entertainment',
      'sightseeing', 'shopping', 'outdoor', 'culture', 'nightlife',
      'relaxation', 'business', 'other'
    ])
    .withMessage('Invalid activity category'),
  
  body('status')
    .optional()
    .isIn(['proposed', 'approved', 'rejected', 'completed'])
    .withMessage('Invalid activity status'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'must-do'])
    .withMessage('Invalid priority level')
];

const voteValidation = [
  body('vote')
    .isIn(['up', 'down', 'remove'])
    .withMessage('Vote must be "up", "down", or "remove"')
];

/**
 * @route   GET /api/trips/:tripId/activities
 * @desc    Get activities for a trip
 * @access  Private
 */
router.get('/trips/:tripId/activities', authenticate, checkTripAccess(), getActivities);

/**
 * @route   POST /api/trips/:tripId/activities
 * @desc    Create a new activity for a trip
 * @access  Private
 */
router.post('/trips/:tripId/activities', authenticate, checkTripAccess(), createActivityValidation, createActivity);

/**
 * @route   GET /api/trips/:tripId/activities/stats
 * @desc    Get activity statistics for a trip
 * @access  Private
 */
router.get('/trips/:tripId/activities/stats', authenticate, checkTripAccess(), getActivityStats);

/**
 * @route   PUT /api/activities/:id
 * @desc    Update an activity
 * @access  Private
 */
router.put('/:id', authenticate, updateActivityValidation, updateActivity);

/**
 * @route   DELETE /api/activities/:id
 * @desc    Delete an activity
 * @access  Private
 */
router.delete('/:id', authenticate, deleteActivity);

/**
 * @route   POST /api/activities/:id/vote
 * @desc    Vote on an activity
 * @access  Private
 */
router.post('/:id/vote', authenticate, voteValidation, voteActivity);

module.exports = router;
