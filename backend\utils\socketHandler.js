const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Trip = require('../models/Trip');
const Message = require('../models/Message');

/**
 * Socket.io authentication middleware
 */
const authenticateSocket = async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    
    if (!token) {
      return next(new Error('Authentication error: No token provided'));
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user || !user.isActive) {
      return next(new Error('Authentication error: Invalid user'));
    }
    
    socket.user = user;
    next();
    
  } catch (error) {
    next(new Error('Authentication error: Invalid token'));
  }
};

/**
 * Handle socket connections and events
 */
const socketHandler = (io) => {
  // Authentication middleware
  io.use(authenticateSocket);
  
  io.on('connection', (socket) => {
    console.log(`User ${socket.user.name} connected (${socket.id})`);
    
    // Join user to their personal room
    socket.join(`user_${socket.user._id}`);
    
    /**
     * Join a trip room
     */
    socket.on('join-trip', async (tripId) => {
      try {
        const trip = await Trip.findById(tripId);
        
        if (!trip) {
          socket.emit('error', { message: 'Trip not found' });
          return;
        }
        
        // Check if user is participant
        const isParticipant = trip.isParticipant(socket.user._id);
        const isCreator = trip.creator.toString() === socket.user._id.toString();
        
        if (!isParticipant && !isCreator) {
          socket.emit('error', { message: 'Access denied to this trip' });
          return;
        }
        
        socket.join(`trip_${tripId}`);
        socket.currentTrip = tripId;
        
        // Notify other participants that user joined
        socket.to(`trip_${tripId}`).emit('user-joined-trip', {
          user: {
            _id: socket.user._id,
            name: socket.user.name,
            profilePhoto: socket.user.profilePhoto
          },
          tripId
        });
        
        console.log(`User ${socket.user.name} joined trip ${tripId}`);
        
      } catch (error) {
        console.error('Join trip error:', error);
        socket.emit('error', { message: 'Failed to join trip' });
      }
    });
    
    /**
     * Leave a trip room
     */
    socket.on('leave-trip', (tripId) => {
      socket.leave(`trip_${tripId}`);
      
      // Notify other participants that user left
      socket.to(`trip_${tripId}`).emit('user-left-trip', {
        user: {
          _id: socket.user._id,
          name: socket.user.name
        },
        tripId
      });
      
      if (socket.currentTrip === tripId) {
        socket.currentTrip = null;
      }
      
      console.log(`User ${socket.user.name} left trip ${tripId}`);
    });
    
    /**
     * Send a chat message
     */
    socket.on('send-message', async (data) => {
      try {
        const { tripId, content, type = 'text', replyTo } = data;
        
        if (!tripId || !content || content.trim().length === 0) {
          socket.emit('error', { message: 'Invalid message data' });
          return;
        }
        
        // Verify user is in trip
        const trip = await Trip.findById(tripId);
        if (!trip || !trip.isParticipant(socket.user._id)) {
          socket.emit('error', { message: 'Access denied to this trip' });
          return;
        }
        
        // Create message
        const message = new Message({
          trip: tripId,
          sender: socket.user._id,
          content: content.trim(),
          type,
          replyTo: replyTo || null
        });
        
        await message.save();
        
        const populatedMessage = await Message.findById(message._id)
          .populate('sender', 'name email profilePhoto')
          .populate('replyTo', 'content sender');
        
        // Broadcast to all trip participants
        io.to(`trip_${tripId}`).emit('new-message', populatedMessage);
        
        // Update trip's last activity
        await Trip.findByIdAndUpdate(tripId, {
          lastActivity: new Date()
        });
        
      } catch (error) {
        console.error('Send message error:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });
    
    /**
     * Vote on an activity
     */
    socket.on('activity-vote', async (data) => {
      try {
        const { activityId, vote } = data;
        
        if (!activityId || !['up', 'down', 'remove'].includes(vote)) {
          socket.emit('error', { message: 'Invalid vote data' });
          return;
        }
        
        const Activity = require('../models/Activity');
        const activity = await Activity.findById(activityId).populate('trip');
        
        if (!activity) {
          socket.emit('error', { message: 'Activity not found' });
          return;
        }
        
        // Check if user is participant
        if (!activity.trip.isParticipant(socket.user._id)) {
          socket.emit('error', { message: 'Access denied to this trip' });
          return;
        }
        
        // Update vote
        if (vote === 'remove') {
          activity.removeVote(socket.user._id);
        } else {
          activity.addVote(socket.user._id, vote);
        }
        
        await activity.save();
        
        const updatedActivity = await Activity.findById(activityId)
          .populate('creator', 'name email profilePhoto')
          .populate('votes.user', 'name email profilePhoto');
        
        // Broadcast vote update to trip participants
        io.to(`trip_${activity.trip._id}`).emit('activity-vote-update', {
          activity: updatedActivity,
          voter: {
            _id: socket.user._id,
            name: socket.user.name
          },
          vote
        });
        
        // Create system message for vote
        if (vote !== 'remove') {
          await Message.createSystemMessage(activity.trip._id, 'activity_voted', {
            userName: socket.user.name,
            activityTitle: activity.title
          });
        }
        
      } catch (error) {
        console.error('Activity vote error:', error);
        socket.emit('error', { message: 'Failed to process vote' });
      }
    });
    
    /**
     * Trip update notification
     */
    socket.on('trip-update', async (data) => {
      try {
        const { tripId, updateType, updateData } = data;
        
        // Broadcast trip update to all participants
        socket.to(`trip_${tripId}`).emit('trip-updated', {
          tripId,
          updateType,
          updateData,
          updatedBy: {
            _id: socket.user._id,
            name: socket.user.name
          }
        });
        
      } catch (error) {
        console.error('Trip update error:', error);
        socket.emit('error', { message: 'Failed to broadcast trip update' });
      }
    });
    
    /**
     * User typing indicator
     */
    socket.on('typing-start', (tripId) => {
      socket.to(`trip_${tripId}`).emit('user-typing', {
        user: {
          _id: socket.user._id,
          name: socket.user.name
        },
        tripId
      });
    });
    
    socket.on('typing-stop', (tripId) => {
      socket.to(`trip_${tripId}`).emit('user-stopped-typing', {
        user: {
          _id: socket.user._id,
          name: socket.user.name
        },
        tripId
      });
    });
    
    /**
     * Handle disconnection
     */
    socket.on('disconnect', () => {
      console.log(`User ${socket.user.name} disconnected (${socket.id})`);
      
      // Notify trip participants if user was in a trip
      if (socket.currentTrip) {
        socket.to(`trip_${socket.currentTrip}`).emit('user-left-trip', {
          user: {
            _id: socket.user._id,
            name: socket.user.name
          },
          tripId: socket.currentTrip
        });
      }
    });
    
    /**
     * Handle errors
     */
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });
  
  return io;
};

module.exports = socketHandler;
