const { validationResult } = require('express-validator');
const Message = require('../models/Message');
const Trip = require('../models/Trip');

/**
 * Get messages for a trip
 * GET /api/trips/:tripId/messages
 */
const getMessages = async (req, res) => {
  try {
    const { tripId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    
    const messages = await Message.findTripMessages(tripId, parseInt(page), parseInt(limit));
    const total = await Message.countDocuments({ 
      trip: tripId, 
      'deleted.isDeleted': { $ne: true } 
    });
    
    // Mark messages as read by current user
    const unreadMessages = messages.filter(msg => 
      !msg.isReadBy(req.user._id) && 
      msg.sender && 
      msg.sender._id.toString() !== req.user._id.toString()
    );
    
    for (const message of unreadMessages) {
      message.markAsRead(req.user._id);
      await message.save();
    }
    
    res.json({
      success: true,
      data: {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
    
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching messages'
    });
  }
};

/**
 * Send a message to a trip
 * POST /api/trips/:tripId/messages
 */
const sendMessage = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const { tripId } = req.params;
    const { content, type = 'text', replyTo, metadata } = req.body;
    
    const message = new Message({
      trip: tripId,
      sender: req.user._id,
      content: content.trim(),
      type,
      replyTo: replyTo || null,
      metadata: metadata || {}
    });
    
    await message.save();
    
    const populatedMessage = await Message.findById(message._id)
      .populate('sender', 'name email profilePhoto')
      .populate('replyTo', 'content sender')
      .populate('reactions.user', 'name');
    
    // Update trip's last activity
    await Trip.findByIdAndUpdate(tripId, {
      lastActivity: new Date()
    });
    
    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        message: populatedMessage
      }
    });
    
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while sending message'
    });
  }
};

/**
 * Edit a message
 * PUT /api/messages/:id
 */
const editMessage = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const { id } = req.params;
    const { content } = req.body;
    
    const message = await Message.findById(id);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }
    
    // Check if user can edit this message
    if (message.sender.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only edit your own messages'
      });
    }
    
    // Check if message is too old to edit (e.g., 15 minutes)
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    if (message.createdAt < fifteenMinutesAgo) {
      return res.status(400).json({
        success: false,
        message: 'Messages can only be edited within 15 minutes of sending'
      });
    }
    
    message.editContent(content.trim());
    await message.save();
    
    const populatedMessage = await Message.findById(id)
      .populate('sender', 'name email profilePhoto')
      .populate('replyTo', 'content sender')
      .populate('reactions.user', 'name');
    
    res.json({
      success: true,
      message: 'Message edited successfully',
      data: {
        message: populatedMessage
      }
    });
    
  } catch (error) {
    console.error('Edit message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while editing message'
    });
  }
};

/**
 * Delete a message
 * DELETE /api/messages/:id
 */
const deleteMessage = async (req, res) => {
  try {
    const { id } = req.params;
    
    const message = await Message.findById(id);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }
    
    // Check if user can delete this message
    if (message.sender.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own messages'
      });
    }
    
    message.softDelete(req.user._id);
    await message.save();
    
    res.json({
      success: true,
      message: 'Message deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting message'
    });
  }
};

/**
 * Add reaction to a message
 * POST /api/messages/:id/react
 */
const addReaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { emoji } = req.body;
    
    if (!emoji || typeof emoji !== 'string' || emoji.length > 10) {
      return res.status(400).json({
        success: false,
        message: 'Invalid emoji'
      });
    }
    
    const message = await Message.findById(id);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }
    
    message.addReaction(req.user._id, emoji);
    await message.save();
    
    const populatedMessage = await Message.findById(id)
      .populate('sender', 'name email profilePhoto')
      .populate('reactions.user', 'name');
    
    res.json({
      success: true,
      message: 'Reaction added successfully',
      data: {
        message: populatedMessage
      }
    });
    
  } catch (error) {
    console.error('Add reaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while adding reaction'
    });
  }
};

/**
 * Remove reaction from a message
 * DELETE /api/messages/:id/react
 */
const removeReaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { emoji } = req.body;
    
    const message = await Message.findById(id);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }
    
    message.removeReaction(req.user._id, emoji);
    await message.save();
    
    const populatedMessage = await Message.findById(id)
      .populate('sender', 'name email profilePhoto')
      .populate('reactions.user', 'name');
    
    res.json({
      success: true,
      message: 'Reaction removed successfully',
      data: {
        message: populatedMessage
      }
    });
    
  } catch (error) {
    console.error('Remove reaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while removing reaction'
    });
  }
};

/**
 * Get unread message count for a trip
 * GET /api/trips/:tripId/messages/unread
 */
const getUnreadCount = async (req, res) => {
  try {
    const { tripId } = req.params;
    
    const count = await Message.getUnreadCount(tripId, req.user._id);
    
    res.json({
      success: true,
      data: {
        unreadCount: count
      }
    });
    
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching unread count'
    });
  }
};

/**
 * Mark all messages as read for a trip
 * POST /api/trips/:tripId/messages/mark-read
 */
const markAllAsRead = async (req, res) => {
  try {
    const { tripId } = req.params;
    
    await Message.markAllAsRead(tripId, req.user._id);
    
    res.json({
      success: true,
      message: 'All messages marked as read'
    });
    
  } catch (error) {
    console.error('Mark all as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while marking messages as read'
    });
  }
};

module.exports = {
  getMessages,
  sendMessage,
  editMessage,
  deleteMessage,
  addReaction,
  removeReaction,
  getUnreadCount,
  markAllAsRead
};
