// User Types
export interface User {
  _id: string;
  name: string;
  email: string;
  profilePhoto?: string;
  bio?: string;
  preferences: UserPreferences;
  trips: string[];
  friends: Friend[];
  lastActive: string;
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  currency: Currency;
  timezone: string;
  notifications: NotificationSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  tripUpdates: boolean;
  activityVotes: boolean;
  messages: boolean;
}

export interface Friend {
  user: User;
  status: "pending" | "accepted" | "blocked";
  addedAt: string;
}

// Trip Types
export interface Trip {
  _id: string;
  name: string;
  description?: string;
  destination?: string;
  startDate: string;
  endDate: string;
  budget: Budget;
  tripCode: string;
  creator: User;
  participants: Participant[];
  activities: string[];
  settings: TripSettings;
  status: TripStatus;
  tags: string[];
  coverImage?: string;
  lastActivity: string;
  createdAt: string;
  updatedAt: string;
  // Virtual fields
  duration?: number;
  remainingBudget?: number;
  participantCount?: number;
  activityCount?: number;
  progress?: number;
}

export interface Budget {
  total: number;
  currency: Currency;
  spent: number;
}

export interface Participant {
  user: User;
  role: ParticipantRole;
  joinedAt: string;
  status: ParticipantStatus;
  permissions: ParticipantPermissions;
}

export interface ParticipantPermissions {
  canEdit: boolean;
  canInvite: boolean;
  canDelete: boolean;
}

export interface TripSettings {
  isPublic: boolean;
  allowVoting: boolean;
  requireApproval: boolean;
  chatEnabled: boolean;
  budgetVisible: boolean;
}

// Activity Types
export interface Activity {
  _id: string;
  title: string;
  description?: string;
  trip: string;
  creator: User;
  date: string;
  time: ActivityTime;
  location: ActivityLocation;
  category: ActivityCategory;
  cost: ActivityCost;
  votes: Vote[];
  priority: Priority;
  status: ActivityStatus;
  tags: string[];
  attachments: Attachment[];
  notes?: string;
  isPublic: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
  // Virtual fields
  voteScore?: number;
  totalVotes?: number;
  upvotes?: number;
  downvotes?: number;
  totalCost?: number;
}

export interface ActivityTime {
  start?: string;
  end?: string;
}

export interface ActivityLocation {
  name?: string;
  address?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ActivityCost {
  amount: number;
  currency: Currency;
  perPerson: boolean;
  notes?: string;
}

export interface Vote {
  user: User;
  vote: "up" | "down";
  votedAt: string;
}

export interface Attachment {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedBy: User;
  uploadedAt: string;
}

// Message Types
export interface Message {
  _id: string;
  trip: string;
  sender: User;
  content: string;
  type: MessageType;
  metadata?: MessageMetadata;
  readBy: MessageRead[];
  reactions: Reaction[];
  replyTo?: Message;
  edited: EditInfo;
  deleted: DeleteInfo;
  createdAt: string;
  updatedAt: string;
}

export interface MessageMetadata {
  action?: SystemAction;
  activityId?: string;
  attachment?: {
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
  };
}

export interface MessageRead {
  user: User;
  readAt: string;
}

export interface Reaction {
  user: User;
  emoji: string;
  reactedAt: string;
}

export interface EditInfo {
  isEdited: boolean;
  editedAt?: string;
  originalContent?: string;
}

export interface DeleteInfo {
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: User;
}

// Enum Types
export type Currency = "USD" | "EUR" | "GBP" | "CAD" | "AUD" | "JPY";
export type ParticipantRole = "creator" | "admin" | "member";
export type ParticipantStatus = "invited" | "joined" | "declined";
export type TripStatus = "planning" | "active" | "completed" | "cancelled";
export type ActivityCategory =
  | "accommodation"
  | "transportation"
  | "food"
  | "entertainment"
  | "sightseeing"
  | "shopping"
  | "outdoor"
  | "culture"
  | "nightlife"
  | "relaxation"
  | "business"
  | "other";
export type Priority = "low" | "medium" | "high" | "must-do";
export type ActivityStatus = "proposed" | "approved" | "rejected" | "completed";
export type MessageType =
  | "text"
  | "image"
  | "file"
  | "system"
  | "activity"
  | "poll";
export type SystemAction =
  | "user_joined"
  | "user_left"
  | "trip_updated"
  | "activity_added"
  | "activity_voted";

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: string[];
}

export interface PaginationInfo {
  current: number;
  pages: number;
  total: number;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface CreateTripForm {
  name: string;
  description?: string;
  destination?: string;
  startDate: string;
  endDate: string;
  budget?: {
    total: number;
    currency: Currency;
  };
  settings?: Partial<TripSettings>;
  tags?: string[];
}

export interface CreateActivityForm {
  title: string;
  description?: string;
  date: string;
  time?: ActivityTime;
  location?: ActivityLocation;
  category: ActivityCategory;
  cost?: ActivityCost;
  priority?: Priority;
  tags?: string[];
  notes?: string;
}

// Context Types
export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  isLoading: boolean;
  isAuthenticated: boolean;
}
