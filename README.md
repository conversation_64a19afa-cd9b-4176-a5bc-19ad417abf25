# Planit - Collaborative Trip Planner

A fullstack web application for collaborative trip planning that allows users to create trips, invite friends, plan activities, vote on them in real-time, and chat about their adventures.

## 🚀 Features

### Core Features
- **User Authentication**: Secure JWT-based authentication with registration and login
- **Trip Management**: Create, edit, and manage collaborative trips with unique trip codes
- **Real-time Collaboration**: Live updates using Socket.io for seamless teamwork
- **Activity Planning**: Add, vote on, and organize trip activities with categories and priorities
- **Group Chat**: Real-time messaging system for each trip
- **Budget Tracking**: Monitor expenses and budget allocation with visual charts
- **Responsive Design**: Mobile-first design that works on all devices

### Advanced Features
- **User Profiles**: Customizable profiles with preferences and settings
- **Trip Invitations**: Invite friends via email or unique trip codes
- **Activity Voting**: Democratic decision-making with upvote/downvote system
- **Timeline View**: Chronological view of trip activities
- **Expense Categories**: Categorized budget tracking with pie charts
- **Confirmation Prompts**: Safety prompts for all destructive actions
- **Data Persistence**: Local storage for user preferences and last active trip

## 🏗️ Architecture

### Backend (Node.js/Express)
- **Framework**: Express.js with TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT tokens with bcrypt password hashing
- **Real-time**: Socket.io for live collaboration
- **Security**: Helmet, CORS, rate limiting, input validation
- **Email**: Nodemailer for trip invitations

### Frontend (React/TypeScript)
- **Framework**: React 18 with TypeScript and Vite
- **Styling**: Tailwind CSS for responsive design
- **State Management**: React Context API with useReducer
- **HTTP Client**: Axios with interceptors
- **Real-time**: Socket.io client
- **Routing**: React Router v6
- **Forms**: React Hook Form with validation
- **Notifications**: React Hot Toast

## 📁 Project Structure

```
planit/
├── backend/                 # Node.js/Express API server
│   ├── config/             # Database configuration
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Authentication & validation
│   ├── models/            # MongoDB/Mongoose models
│   ├── routes/            # API routes
│   ├── utils/             # Helper functions
│   ├── .env               # Environment variables
│   ├── package.json       # Dependencies and scripts
│   └── server.js          # Main server file
├── frontend/               # React/TypeScript client
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── context/       # React Context providers
│   │   ├── hooks/         # Custom React hooks
│   │   ├── pages/         # Page components
│   │   ├── services/      # API service functions
│   │   ├── types/         # TypeScript type definitions
│   │   └── utils/         # Helper functions
│   ├── .env               # Environment variables
│   └── package.json       # Dependencies and scripts
└── README.md              # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd planit
   ```

2. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Install frontend dependencies**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Set up environment variables**
   
   Backend (.env):
   ```env
   MONGO_URI=mongodb://localhost:27017/planit_db
   JWT_SECRET=your_super_secret_jwt_key
   JWT_EXPIRES_IN=7d
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:5173
   ```
   
   Frontend (.env):
   ```env
   VITE_API_BASE_URL=http://localhost:5000/api
   VITE_SOCKET_URL=http://localhost:5000
   VITE_APP_NAME=Planit
   ```

5. **Start the development servers**
   
   Backend:
   ```bash
   cd backend
   npm run dev
   ```
   
   Frontend (in a new terminal):
   ```bash
   cd frontend
   npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5000

## 📊 Database Schema

### Collections
- **users**: User accounts and profiles
- **trips**: Trip information and settings
- **activities**: Trip activities with voting
- **messages**: Chat messages for trips

### Key Relationships
- Users can create and participate in multiple trips
- Trips contain multiple activities and messages
- Activities have votes from trip participants
- Messages support reactions and replies

## 🔐 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update profile

### Trips
- `GET /api/trips` - Get user's trips
- `POST /api/trips` - Create new trip
- `GET /api/trips/:id` - Get trip details
- `PUT /api/trips/:id` - Update trip
- `DELETE /api/trips/:id` - Delete trip
- `POST /api/trips/join` - Join trip with code

### Activities
- `GET /api/trips/:tripId/activities` - Get trip activities
- `POST /api/trips/:tripId/activities` - Create activity
- `PUT /api/activities/:id` - Update activity
- `DELETE /api/activities/:id` - Delete activity
- `POST /api/activities/:id/vote` - Vote on activity

### Messages
- `GET /api/trips/:tripId/messages` - Get trip messages
- `POST /api/trips/:tripId/messages` - Send message
- `PUT /api/messages/:id` - Edit message
- `DELETE /api/messages/:id` - Delete message

## 🔄 Real-time Events

### Socket.io Events
- `join-trip` - Join trip room
- `leave-trip` - Leave trip room
- `send-message` - Send chat message
- `new-message` - Receive new message
- `activity-vote` - Vote on activity
- `activity-vote-update` - Receive vote update
- `user-typing` - Typing indicators

## 🧪 Testing

### Backend Testing
```bash
cd backend
npm test
```

### Frontend Testing
```bash
cd frontend
npm test
```

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Use PM2 for process management
3. Set up reverse proxy (nginx)
4. Use MongoDB Atlas for database

### Frontend Deployment
1. Build the application: `npm run build`
2. Deploy to hosting service (Netlify, Vercel, etc.)
3. Update API URLs for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📝 License

MIT License - see LICENSE file for details

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

---

**Happy Trip Planning! 🌍✈️**
