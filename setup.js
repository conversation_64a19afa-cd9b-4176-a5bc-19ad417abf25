#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Planit - Collaborative Trip Planner\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ Node.js version 16 or higher is required');
  console.error(`Current version: ${nodeVersion}`);
  process.exit(1);
}

console.log('✅ Node.js version check passed');

// Function to run command and handle errors
function runCommand(command, cwd = process.cwd()) {
  try {
    console.log(`📦 Running: ${command}`);
    execSync(command, { cwd, stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`❌ Failed to run: ${command}`);
    console.error(error.message);
    return false;
  }
}

// Function to create .env file if it doesn't exist
function createEnvFile(filePath, content) {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Created ${filePath}`);
  } else {
    console.log(`⚠️  ${filePath} already exists, skipping...`);
  }
}

// Install backend dependencies
console.log('\n📦 Installing backend dependencies...');
if (!runCommand('npm install', './backend')) {
  console.error('❌ Failed to install backend dependencies');
  process.exit(1);
}

// Install frontend dependencies
console.log('\n📦 Installing frontend dependencies...');
if (!runCommand('npm install', './frontend')) {
  console.error('❌ Failed to install frontend dependencies');
  process.exit(1);
}

// Create backend .env file
console.log('\n🔧 Setting up environment files...');
const backendEnv = `# Database Configuration
MONGO_URI=mongodb://localhost:27017/planit_db
# For production, use MongoDB Atlas:
# MONGO_URI=mongodb+srv://username:<EMAIL>/planit_db

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Email Configuration (for invitations)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Socket.io Configuration
SOCKET_CORS_ORIGIN=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
`;

createEnvFile('./backend/.env', backendEnv);

// Create frontend .env file
const frontendEnv = `# Backend API Configuration
VITE_API_BASE_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000

# App Configuration
VITE_APP_NAME=Planit
VITE_APP_VERSION=1.0.0

# File Upload Configuration
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/plain

# Map Configuration (if using maps)
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Environment
VITE_NODE_ENV=development
`;

createEnvFile('./frontend/.env', frontendEnv);

// Create package.json scripts for easy development
const rootPackageJson = {
  "name": "planit-collaborative-trip-planner",
  "version": "1.0.0",
  "description": "A fullstack collaborative trip planning application",
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "cd backend && npm run dev",
    "dev:frontend": "cd frontend && npm run dev",
    "build": "cd frontend && npm run build",
    "start": "cd backend && npm start",
    "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install",
    "test": "cd backend && npm test && cd ../frontend && npm test"
  },
  "devDependencies": {
    "concurrently": "^8.2.2"
  },
  "keywords": [
    "trip-planner",
    "collaborative",
    "travel",
    "react",
    "nodejs",
    "mongodb"
  ],
  "author": "Planit Team",
  "license": "MIT"
};

if (!fs.existsSync('./package.json')) {
  fs.writeFileSync('./package.json', JSON.stringify(rootPackageJson, null, 2));
  console.log('✅ Created root package.json');
  
  // Install concurrently for running both servers
  console.log('\n📦 Installing development tools...');
  runCommand('npm install');
}

console.log('\n🎉 Setup completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Make sure MongoDB is running on your system');
console.log('2. Update the .env files with your actual configuration');
console.log('3. Run the development servers:');
console.log('   npm run dev');
console.log('\n🌐 The application will be available at:');
console.log('   Frontend: http://localhost:5173');
console.log('   Backend:  http://localhost:5000');
console.log('\n📚 Check the README.md for detailed documentation');
console.log('\nHappy coding! 🚀');
