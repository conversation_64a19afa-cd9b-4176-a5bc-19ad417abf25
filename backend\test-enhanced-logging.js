const axios = require('axios');

/**
 * Test script to demonstrate enhanced logging and error handling
 */

const BASE_URL = 'http://localhost:5000';

async function testEnhancedLogging() {
  console.log('🧪 Testing Enhanced Backend Logging and Error Handling\n');

  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check Response:', {
      status: healthResponse.data.status,
      database: healthResponse.data.database.status,
      network: healthResponse.data.network.connected
    });

    // Test 2: Valid Account Creation
    console.log('\n2️⃣ Testing Valid Account Creation...');
    try {
      const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        password: 'password123'
      });
      console.log('✅ Account Creation Success:', registerResponse.data.success);
    } catch (error) {
      console.log('ℹ️ Account Creation Response:', error.response?.data || error.message);
    }

    // Test 3: Invalid Account Creation (Missing Fields)
    console.log('\n3️⃣ Testing Invalid Account Creation (Missing Fields)...');
    try {
      await axios.post(`${BASE_URL}/api/auth/register`, {
        name: 'Test User'
        // Missing email and password
      });
    } catch (error) {
      console.log('✅ Validation Error Caught:', error.response?.data?.message);
    }

    // Test 4: Duplicate Account Creation
    console.log('\n4️⃣ Testing Duplicate Account Creation...');
    try {
      await axios.post(`${BASE_URL}/api/auth/register`, {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123'
      });
      
      // Try to create the same account again
      await axios.post(`${BASE_URL}/api/auth/register`, {
        name: 'Test User 2',
        email: '<EMAIL>',
        password: 'password456'
      });
    } catch (error) {
      console.log('✅ Duplicate Account Error:', error.response?.data?.message);
    }

    // Test 5: Page Transition Simulation
    console.log('\n5️⃣ Testing Page Transition Tracking...');
    await axios.get(`${BASE_URL}/health`, {
      headers: {
        'Referer': 'http://localhost:5173/login'
      }
    });
    console.log('✅ Page transition logged (check server console)');

    // Test 6: Invalid Route
    console.log('\n6️⃣ Testing Invalid Route...');
    try {
      await axios.get(`${BASE_URL}/api/invalid-route`);
    } catch (error) {
      console.log('✅ 404 Error Handled:', error.response?.data?.message);
    }

  } catch (error) {
    console.error('❌ Test Error:', error.message);
  }
}

// Run the tests
testEnhancedLogging().then(() => {
  console.log('\n🎉 Enhanced Logging Tests Completed!');
  console.log('📋 Check the server console to see detailed logging output');
  process.exit(0);
}).catch(error => {
  console.error('❌ Test Suite Failed:', error.message);
  process.exit(1);
});
