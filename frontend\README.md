# Planit Frontend - Collaborative Trip Planner

This is the frontend application for Planit, a collaborative trip planning platform built with React, TypeScript, and Vite.

## Features

- **User Authentication**: Login and registration with JWT tokens
- **Trip Management**: Create, edit, and manage collaborative trips
- **Real-time Collaboration**: Live updates using Socket.io
- **Activity Planning**: Add, vote on, and organize trip activities
- **Group Chat**: Real-time messaging for each trip
- **Budget Tracking**: Monitor expenses and budget allocation
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Data Persistence**: Local storage for user preferences

## Tech Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Context + useReducer
- **HTTP Client**: Axios
- **Real-time**: Socket.io Client
- **Routing**: React Router v6
- **Forms**: React Hook Form with validation
- **Charts**: Chart.js or Recharts for budget visualization
- **Icons**: Lucide React or Heroicons

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager
- Backend API server running (see backend README)

## Installation

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file with your configuration values.

## Environment Variables

Create a `.env` file in the frontend root directory with the following variables:

```env
# Backend API
VITE_API_BASE_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000

# App Configuration
VITE_APP_NAME=Planit
VITE_APP_VERSION=1.0.0

# File Upload
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Optional: Google Maps API Key
VITE_GOOGLE_MAPS_API_KEY=your_api_key_here
```

## Running the Application

### Development Mode
```bash
npm run dev
```
This starts the development server with hot reload at `http://localhost:5173`

### Build for Production
```bash
npm run build
```
This creates an optimized production build in the `dist` folder.

### Preview Production Build
```bash
npm run preview
```
This serves the production build locally for testing.

### Linting and Type Checking
```bash
npm run lint        # ESLint
npm run type-check  # TypeScript compiler
```

## Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── ui/           # Basic UI components (Button, Input, etc.)
│   │   ├── forms/        # Form components
│   │   ├── layout/       # Layout components (Header, Sidebar, etc.)
│   │   └── common/       # Common components
│   ├── pages/            # Page components
│   │   ├── auth/         # Authentication pages
│   │   ├── dashboard/    # Dashboard pages
│   │   ├── trips/        # Trip-related pages
│   │   └── profile/      # User profile pages
│   ├── hooks/            # Custom React hooks
│   ├── context/          # React Context providers
│   ├── utils/            # Utility functions
│   ├── services/         # API services
│   ├── types/            # TypeScript type definitions
│   ├── constants/        # App constants
│   └── styles/           # Global styles
├── .env                  # Environment variables
├── package.json          # Dependencies and scripts
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
└── vite.config.ts        # Vite configuration
```

## Key Features Implementation

### Authentication
- JWT token storage in localStorage
- Automatic token refresh
- Protected routes with authentication guards
- Login/logout functionality

### Real-time Features
- Socket.io connection management
- Live chat messaging
- Real-time activity voting
- Trip update notifications
- User presence indicators

### State Management
- AuthContext for user authentication state
- TripContext for current trip data
- SocketContext for real-time connections
- Local storage persistence for user preferences

### Responsive Design
- Mobile-first approach with Tailwind CSS
- Responsive navigation and layouts
- Touch-friendly interface elements
- Optimized for various screen sizes

## API Integration

The frontend communicates with the backend API using:
- Axios for HTTP requests
- Socket.io for real-time communication
- JWT tokens for authentication
- Error handling and loading states

## Testing

```bash
npm run test        # Run unit tests
npm run test:watch  # Run tests in watch mode
npm run test:ui     # Run tests with UI
```

## Deployment

### Build and Deploy
1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy the `dist` folder to your hosting service:
   - **Netlify**: Drag and drop the `dist` folder
   - **Vercel**: Connect your Git repository
   - **AWS S3**: Upload to S3 bucket with static hosting
   - **Traditional hosting**: Upload `dist` contents to web server

### Environment Configuration
- Set production environment variables in your hosting platform
- Update `VITE_API_BASE_URL` to point to your production API
- Configure CORS settings in the backend for your domain

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Optimization

- Code splitting with React.lazy()
- Image optimization and lazy loading
- Bundle size optimization with Vite
- Caching strategies for API responses
- Service worker for offline functionality (optional)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## Troubleshooting

### Common Issues

1. **API Connection Issues**
   - Check if backend server is running
   - Verify API URL in environment variables
   - Check CORS configuration

2. **Socket.io Connection Problems**
   - Ensure Socket.io server is running
   - Check firewall settings
   - Verify authentication tokens

3. **Build Issues**
   - Clear node_modules and reinstall dependencies
   - Check TypeScript errors
   - Verify environment variables

## License

MIT License - see LICENSE file for details
