import { apiService } from './api';
import { Trip, CreateTripForm, Activity, Message } from '../types';

interface TripsResponse {
  trips: Trip[];
  pagination: {
    current: number;
    pages: number;
    total: number;
  };
}

export const tripService = {
  /**
   * Get all trips for the current user
   */
  getTrips: async (params?: {
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<TripsResponse> => {
    return apiService.get<TripsResponse>('/trips', params);
  },

  /**
   * Get a specific trip by ID
   */
  getTripById: async (tripId: string): Promise<Trip> => {
    return apiService.get<{ trip: Trip }>(`/trips/${tripId}`).then(response => response.trip);
  },

  /**
   * Create a new trip
   */
  createTrip: async (tripData: CreateTripForm): Promise<Trip> => {
    const response = await apiService.post<{ trip: Trip }>('/trips', tripData);
    return response.trip;
  },

  /**
   * Update a trip
   */
  updateTrip: async (tripId: string, tripData: Partial<Trip>): Promise<Trip> => {
    const response = await apiService.put<{ trip: Trip }>(`/trips/${tripId}`, tripData);
    return response.trip;
  },

  /**
   * Delete a trip
   */
  deleteTrip: async (tripId: string): Promise<void> => {
    await apiService.delete(`/trips/${tripId}`);
  },

  /**
   * Join a trip using trip code
   */
  joinTrip: async (tripCode: string): Promise<Trip> => {
    const response = await apiService.post<{ trip: Trip }>('/trips/join', { tripCode });
    return response.trip;
  },

  /**
   * Get activities for a trip
   */
  getTripActivities: async (tripId: string, params?: {
    category?: string;
    status?: string;
    date?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    activities: Activity[];
    pagination: {
      current: number;
      pages: number;
      total: number;
    };
  }> => {
    return apiService.get(`/trips/${tripId}/activities`, params);
  },

  /**
   * Create a new activity for a trip
   */
  createActivity: async (tripId: string, activityData: any): Promise<Activity> => {
    const response = await apiService.post<{ activity: Activity }>(`/trips/${tripId}/activities`, activityData);
    return response.activity;
  },

  /**
   * Update an activity
   */
  updateActivity: async (activityId: string, activityData: Partial<Activity>): Promise<Activity> => {
    const response = await apiService.put<{ activity: Activity }>(`/activities/${activityId}`, activityData);
    return response.activity;
  },

  /**
   * Delete an activity
   */
  deleteActivity: async (activityId: string): Promise<void> => {
    await apiService.delete(`/activities/${activityId}`);
  },

  /**
   * Vote on an activity
   */
  voteActivity: async (activityId: string, vote: 'up' | 'down' | 'remove'): Promise<Activity> => {
    const response = await apiService.post<{ activity: Activity }>(`/activities/${activityId}/vote`, { vote });
    return response.activity;
  },

  /**
   * Get activity statistics for a trip
   */
  getActivityStats: async (tripId: string): Promise<{
    totalActivities: number;
    totalCost: number;
    categoryBreakdown: Record<string, number>;
    statusBreakdown: Record<string, number>;
  }> => {
    return apiService.get<{ stats: any }>(`/trips/${tripId}/activities/stats`).then(response => response.stats);
  },

  /**
   * Get messages for a trip
   */
  getTripMessages: async (tripId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<{
    messages: Message[];
    pagination: {
      current: number;
      pages: number;
      total: number;
    };
  }> => {
    return apiService.get(`/trips/${tripId}/messages`, params);
  },

  /**
   * Send a message to a trip
   */
  sendMessage: async (tripId: string, messageData: {
    content: string;
    type?: string;
    replyTo?: string;
  }): Promise<Message> => {
    const response = await apiService.post<{ message: Message }>(`/trips/${tripId}/messages`, messageData);
    return response.message;
  },

  /**
   * Edit a message
   */
  editMessage: async (messageId: string, content: string): Promise<Message> => {
    const response = await apiService.put<{ message: Message }>(`/messages/${messageId}`, { content });
    return response.message;
  },

  /**
   * Delete a message
   */
  deleteMessage: async (messageId: string): Promise<void> => {
    await apiService.delete(`/messages/${messageId}`);
  },

  /**
   * Add reaction to a message
   */
  addReaction: async (messageId: string, emoji: string): Promise<Message> => {
    const response = await apiService.post<{ message: Message }>(`/messages/${messageId}/react`, { emoji });
    return response.message;
  },

  /**
   * Remove reaction from a message
   */
  removeReaction: async (messageId: string, emoji: string): Promise<Message> => {
    const response = await apiService.delete<{ message: Message }>(`/messages/${messageId}/react`);
    return response.message;
  },

  /**
   * Get unread message count for a trip
   */
  getUnreadCount: async (tripId: string): Promise<number> => {
    return apiService.get<{ unreadCount: number }>(`/trips/${tripId}/messages/unread`).then(response => response.unreadCount);
  },

  /**
   * Mark all messages as read for a trip
   */
  markAllAsRead: async (tripId: string): Promise<void> => {
    await apiService.post(`/trips/${tripId}/messages/mark-read`);
  },
};
