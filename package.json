{"name": "planit-collaborative-trip-planner", "version": "1.0.0", "description": "A fullstack collaborative trip planning application", "scripts": {"setup": "node setup.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "test": "cd backend && npm test && cd ../frontend && npm test", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules", "reset": "npm run clean && npm run install:all"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["trip-planner", "collaborative", "travel", "react", "nodejs", "mongodb", "socket.io", "jwt", "tailwind"], "author": "Planit Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/planit-trip-planner.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}