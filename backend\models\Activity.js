const mongoose = require('mongoose');

const activitySchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Activity title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    default: ''
  },
  trip: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trip',
    required: true
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  date: {
    type: Date,
    required: [true, 'Activity date is required']
  },
  time: {
    start: {
      type: String,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
    },
    end: {
      type: String,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
    }
  },
  location: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Location name cannot exceed 100 characters']
    },
    address: {
      type: String,
      trim: true,
      maxlength: [200, 'Address cannot exceed 200 characters']
    },
    coordinates: {
      latitude: {
        type: Number,
        min: [-90, 'Latitude must be between -90 and 90'],
        max: [90, 'Latitude must be between -90 and 90']
      },
      longitude: {
        type: Number,
        min: [-180, 'Longitude must be between -180 and 180'],
        max: [180, 'Longitude must be between -180 and 180']
      }
    }
  },
  category: {
    type: String,
    required: [true, 'Activity category is required'],
    enum: [
      'accommodation',
      'transportation',
      'food',
      'entertainment',
      'sightseeing',
      'shopping',
      'outdoor',
      'culture',
      'nightlife',
      'relaxation',
      'business',
      'other'
    ]
  },
  cost: {
    amount: {
      type: Number,
      min: [0, 'Cost cannot be negative'],
      default: 0
    },
    currency: {
      type: String,
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY']
    },
    perPerson: {
      type: Boolean,
      default: true
    },
    notes: {
      type: String,
      maxlength: [200, 'Cost notes cannot exceed 200 characters']
    }
  },
  votes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    vote: {
      type: String,
      enum: ['up', 'down'],
      required: true
    },
    votedAt: {
      type: Date,
      default: Date.now
    }
  }],
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'must-do'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['proposed', 'approved', 'rejected', 'completed'],
    default: 'proposed'
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [20, 'Tag cannot exceed 20 characters']
  }],
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String,
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for vote score
activitySchema.virtual('voteScore').get(function() {
  if (!this.votes || this.votes.length === 0) return 0;
  
  return this.votes.reduce((score, vote) => {
    return score + (vote.vote === 'up' ? 1 : -1);
  }, 0);
});

// Virtual for total votes
activitySchema.virtual('totalVotes').get(function() {
  return this.votes ? this.votes.length : 0;
});

// Virtual for upvotes count
activitySchema.virtual('upvotes').get(function() {
  return this.votes ? this.votes.filter(v => v.vote === 'up').length : 0;
});

// Virtual for downvotes count
activitySchema.virtual('downvotes').get(function() {
  return this.votes ? this.votes.filter(v => v.vote === 'down').length : 0;
});

// Virtual for total cost calculation
activitySchema.virtual('totalCost').get(function() {
  if (!this.cost.amount) return 0;
  
  // If cost is per person, we'll need trip participant count
  // This would be calculated in the application logic
  return this.cost.amount;
});

// Indexes for better query performance
activitySchema.index({ trip: 1, date: 1 });
activitySchema.index({ creator: 1 });
activitySchema.index({ category: 1 });
activitySchema.index({ status: 1 });
activitySchema.index({ 'votes.user': 1 });
activitySchema.index({ priority: 1 });

// Compound index for trip activities ordered by date and order
activitySchema.index({ trip: 1, date: 1, order: 1 });

// Instance method to check if user has voted
activitySchema.methods.hasUserVoted = function(userId) {
  return this.votes.some(vote => vote.user.toString() === userId.toString());
};

// Instance method to get user's vote
activitySchema.methods.getUserVote = function(userId) {
  const vote = this.votes.find(vote => vote.user.toString() === userId.toString());
  return vote ? vote.vote : null;
};

// Instance method to add or update vote
activitySchema.methods.addVote = function(userId, voteType) {
  const existingVoteIndex = this.votes.findIndex(
    vote => vote.user.toString() === userId.toString()
  );
  
  if (existingVoteIndex !== -1) {
    // Update existing vote
    this.votes[existingVoteIndex].vote = voteType;
    this.votes[existingVoteIndex].votedAt = new Date();
  } else {
    // Add new vote
    this.votes.push({
      user: userId,
      vote: voteType,
      votedAt: new Date()
    });
  }
};

// Instance method to remove vote
activitySchema.methods.removeVote = function(userId) {
  this.votes = this.votes.filter(
    vote => vote.user.toString() !== userId.toString()
  );
};

// Static method to find activities by trip
activitySchema.statics.findByTrip = function(tripId) {
  return this.find({ trip: tripId })
    .populate('creator', 'name email profilePhoto')
    .populate('votes.user', 'name email profilePhoto')
    .sort({ date: 1, order: 1 });
};

// Static method to find activities by category
activitySchema.statics.findByCategory = function(tripId, category) {
  return this.find({ trip: tripId, category: category })
    .populate('creator', 'name email profilePhoto')
    .sort({ date: 1, order: 1 });
};

module.exports = mongoose.model('Activity', activitySchema);
