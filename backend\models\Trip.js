const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const tripSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Trip name is required'],
    trim: true,
    maxlength: [100, 'Trip name cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    default: ''
  },
  destination: {
    type: String,
    trim: true,
    maxlength: [100, 'Destination cannot exceed 100 characters']
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required'],
    validate: {
      validator: function(value) {
        return value > this.startDate;
      },
      message: 'End date must be after start date'
    }
  },
  budget: {
    total: {
      type: Number,
      min: [0, 'Budget cannot be negative'],
      default: 0
    },
    currency: {
      type: String,
      default: 'USD',
      enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY']
    },
    spent: {
      type: Number,
      default: 0,
      min: [0, 'Spent amount cannot be negative']
    }
  },
  tripCode: {
    type: String,
    unique: true,
    default: function() {
      return uuidv4().substring(0, 8).toUpperCase();
    }
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['creator', 'admin', 'member'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['invited', 'joined', 'declined'],
      default: 'joined'
    },
    permissions: {
      canEdit: {
        type: Boolean,
        default: true
      },
      canInvite: {
        type: Boolean,
        default: false
      },
      canDelete: {
        type: Boolean,
        default: false
      }
    }
  }],
  activities: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Activity'
  }],
  settings: {
    isPublic: {
      type: Boolean,
      default: false
    },
    allowVoting: {
      type: Boolean,
      default: true
    },
    requireApproval: {
      type: Boolean,
      default: false
    },
    chatEnabled: {
      type: Boolean,
      default: true
    },
    budgetVisible: {
      type: Boolean,
      default: true
    }
  },
  status: {
    type: String,
    enum: ['planning', 'active', 'completed', 'cancelled'],
    default: 'planning'
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [20, 'Tag cannot exceed 20 characters']
  }],
  coverImage: {
    type: String,
    default: null
  },
  lastActivity: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for trip duration in days
tripSchema.virtual('duration').get(function() {
  if (this.startDate && this.endDate) {
    const diffTime = Math.abs(this.endDate - this.startDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  return 0;
});

// Virtual for remaining budget
tripSchema.virtual('remainingBudget').get(function() {
  return Math.max(0, this.budget.total - this.budget.spent);
});

// Virtual for participant count
tripSchema.virtual('participantCount').get(function() {
  return this.participants ? this.participants.filter(p => p.status === 'joined').length : 0;
});

// Virtual for activity count
tripSchema.virtual('activityCount').get(function() {
  return this.activities ? this.activities.length : 0;
});

// Virtual for trip progress (0-100%)
tripSchema.virtual('progress').get(function() {
  const now = new Date();
  if (now < this.startDate) return 0;
  if (now > this.endDate) return 100;
  
  const totalDuration = this.endDate - this.startDate;
  const elapsed = now - this.startDate;
  return Math.round((elapsed / totalDuration) * 100);
});

// Indexes for better query performance
tripSchema.index({ creator: 1 });
tripSchema.index({ 'participants.user': 1 });
tripSchema.index({ tripCode: 1 });
tripSchema.index({ startDate: 1, endDate: 1 });
tripSchema.index({ status: 1 });
tripSchema.index({ lastActivity: -1 });

// Update lastActivity on save
tripSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastActivity = new Date();
  }
  next();
});

// Static method to find by trip code
tripSchema.statics.findByCode = function(code) {
  return this.findOne({ tripCode: code.toUpperCase() });
};

// Static method to find user's trips
tripSchema.statics.findUserTrips = function(userId) {
  return this.find({
    $or: [
      { creator: userId },
      { 'participants.user': userId }
    ]
  }).populate('creator', 'name email profilePhoto')
    .populate('participants.user', 'name email profilePhoto')
    .sort({ lastActivity: -1 });
};

// Instance method to check if user is participant
tripSchema.methods.isParticipant = function(userId) {
  return this.participants.some(p => 
    p.user.toString() === userId.toString() && p.status === 'joined'
  );
};

// Instance method to get user's role in trip
tripSchema.methods.getUserRole = function(userId) {
  const participant = this.participants.find(p => 
    p.user.toString() === userId.toString()
  );
  return participant ? participant.role : null;
};

// Instance method to check user permissions
tripSchema.methods.canUserEdit = function(userId) {
  if (this.creator.toString() === userId.toString()) return true;
  
  const participant = this.participants.find(p => 
    p.user.toString() === userId.toString()
  );
  return participant ? participant.permissions.canEdit : false;
};

module.exports = mongoose.model('Trip', tripSchema);
