import React from 'react';
import { Link } from 'react-router-dom';
import { Plus, MapPin, Users, Calendar, TrendingUp } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-8 text-white">
        <h1 className="text-3xl font-bold mb-2">
          Welcome back, {user?.name?.split(' ')[0]}! 👋
        </h1>
        <p className="text-blue-100 mb-6">
          Ready to plan your next adventure? Create a new trip or continue working on existing ones.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Link to="/trips/create">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <Plus className="h-5 w-5 mr-2" />
              Create New Trip
            </Button>
          </Link>
          <Link to="/friends-trips">
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Users className="h-5 w-5 mr-2" />
              View Friends' Trips
            </Button>
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
            <MapPin className="h-6 w-6 text-blue-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-1">0</h3>
          <p className="text-gray-600">Active Trips</p>
        </Card>

        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
            <Users className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-1">0</h3>
          <p className="text-gray-600">Trip Collaborators</p>
        </Card>

        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
            <Calendar className="h-6 w-6 text-purple-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-1">0</h3>
          <p className="text-gray-600">Upcoming Activities</p>
        </Card>

        <Card className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-4">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-1">$0</h3>
          <p className="text-gray-600">Total Budget</p>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Trips */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Trips</h2>
            <Link to="/trips" className="text-blue-600 hover:text-blue-500 text-sm font-medium">
              View all
            </Link>
          </div>
          <div className="space-y-3">
            <div className="text-center py-8 text-gray-500">
              <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">No trips yet</p>
              <p className="text-sm">Create your first trip to get started!</p>
              <Link to="/trips/create" className="mt-4 inline-block">
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Trip
                </Button>
              </Link>
            </div>
          </div>
        </Card>

        {/* Upcoming Activities */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Upcoming Activities</h2>
            <Link to="/calendar" className="text-blue-600 hover:text-blue-500 text-sm font-medium">
              View calendar
            </Link>
          </div>
          <div className="space-y-3">
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">No activities scheduled</p>
              <p className="text-sm">Add activities to your trips to see them here.</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Getting Started Guide */}
      <Card>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Getting Started with Planit</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
              <span className="text-blue-600 font-bold text-lg">1</span>
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Create Your First Trip</h3>
            <p className="text-sm text-gray-600">Set up a new trip with dates, destination, and budget to get started.</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
              <span className="text-green-600 font-bold text-lg">2</span>
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Invite Friends</h3>
            <p className="text-sm text-gray-600">Share your trip code with friends so they can join and collaborate.</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
              <span className="text-purple-600 font-bold text-lg">3</span>
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Plan Activities</h3>
            <p className="text-sm text-gray-600">Add activities, vote on them, and create the perfect itinerary together.</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;
