const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");
const { createServer } = require("http");
const { Server } = require("socket.io");
require("dotenv").config();

// Import database connection
const connectDB = require("./config/database");

// Import enhanced logging and error handling middleware
const {
  requestLogger,
  pageTransitionLogger,
  networkErrorHandler,
  setupDatabaseMonitoring,
} = require("./middleware/logging");
const {
  generalNetworkErrorHandler,
} = require("./middleware/networkErrorHandler");

// Import routes
const authRoutes = require("./routes/auth");
const tripRoutes = require("./routes/trips");
const activityRoutes = require("./routes/activities");
const messageRoutes = require("./routes/messages");
const userRoutes = require("./routes/users");

// Import socket handlers
const socketHandler = require("./utils/socketHandler");

const app = express();
const server = createServer(app);

// Initialize Socket.io
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"],
  },
});

// Setup database monitoring for real-time status updates
setupDatabaseMonitoring();

// Connect to MongoDB with enhanced error handling
connectDB().catch((err) => {
  console.error("❌ Failed to connect to MongoDB:", err.message);
  process.exit(1);
});

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP, please try again later.",
});
app.use(limiter);

// CORS configuration
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    credentials: true,
  })
);

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Static files for uploads
app.use("/uploads", express.static("uploads"));

// Enhanced logging middleware
app.use(requestLogger);
app.use(pageTransitionLogger);

// Network error handling middleware (before routes)
app.use(generalNetworkErrorHandler);

// Enhanced health check endpoint with comprehensive system status
app.get("/health", async (req, res) => {
  const {
    checkNetworkConnectivity,
    checkDatabaseHealth,
  } = require("./middleware/networkErrorHandler");

  try {
    const dbState = require("mongoose").connection.readyState;
    const dbStatus =
      dbState === 1
        ? "Connected"
        : dbState === 2
        ? "Connecting"
        : "Disconnected";

    // Check network and database health
    const networkStatus = await checkNetworkConnectivity();
    const dbHealth = await checkDatabaseHealth();

    const healthData = {
      status: "OK",
      message: "Planit API Server is running with enhanced monitoring",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || "development",
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        pid: process.pid,
        platform: process.platform,
        nodeVersion: process.version,
      },
      database: {
        status: dbStatus,
        readyState: dbState,
        healthy: dbHealth.healthy,
        host: require("mongoose").connection.host || "Unknown",
        name: require("mongoose").connection.name || "Unknown",
      },
      network: {
        connected: networkStatus.connected,
        message: networkStatus.message,
      },
      features: {
        enhancedLogging: true,
        pageTransitionTracking: true,
        networkErrorDetection: true,
        databaseMonitoring: true,
        accountCreationErrorHandling: true,
      },
    };

    // Log health check request
    console.log(`🏥 Health check requested from ${req.ip}`.info);

    res.status(200).json(healthData);
  } catch (error) {
    console.error(`❌ Health check error:`.error, error.message);
    res.status(500).json({
      status: "ERROR",
      message: "Health check failed",
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// API Routes
app.use("/api/auth", authRoutes);
app.use("/api/trips", tripRoutes);
app.use("/api/activities", activityRoutes);
app.use("/api/messages", messageRoutes);
app.use("/api/users", userRoutes);

// Socket.io connection handling
socketHandler(io);

// Enhanced error handling middleware
app.use(networkErrorHandler);

// Final error handler
app.use((err, req, res, next) => {
  // Skip if response already sent
  if (res.headersSent) {
    return next(err);
  }

  console.error("🚨 Unhandled Error:".red.bold, err.stack);

  // Mongoose validation error
  if (err.name === "ValidationError") {
    const errors = Object.values(err.errors).map((e) => e.message);
    return res.status(400).json({
      success: false,
      message: "Validation Error",
      errors,
      timestamp: new Date().toISOString(),
    });
  }

  // JWT error
  if (err.name === "JsonWebTokenError") {
    return res.status(401).json({
      success: false,
      message: "Invalid token",
      timestamp: new Date().toISOString(),
    });
  }

  // MongoDB duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json({
      success: false,
      message: `${field} already exists`,
      timestamp: new Date().toISOString(),
    });
  }

  // Default error
  res.status(err.status || 500).json({
    success: false,
    message: err.message || "Internal Server Error",
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
  });
});

const PORT = process.env.PORT || 5000;

// Enhanced server startup with comprehensive logging
server.listen(PORT, () => {
  const colors = require("colors");

  // Configure colors
  colors.setTheme({
    info: "cyan",
    warn: "yellow",
    error: "red",
    success: "green",
    debug: "blue",
    verbose: "magenta",
  });

  console.log("\n" + "🚀".repeat(60).success);
  console.log("🎉 PLANIT API SERVER STARTED SUCCESSFULLY!".success.bold);
  console.log("🚀".repeat(60).success);

  console.log(`\n📊 SERVER INFORMATION:`.info.bold);
  console.log(`   🌐 Port: ${PORT}`.info);
  console.log(
    `   📊 Environment: ${process.env.NODE_ENV || "development"}`.info
  );
  console.log(
    `   🔗 Frontend URL: ${process.env.FRONTEND_URL || "http://localhost:5173"}`
      .info
  );
  console.log(`   ⏰ Started at: ${new Date().toISOString()}`.info);

  console.log(`\n🗄️  DATABASE STATUS:`.debug.bold);
  const dbState = require("mongoose").connection.readyState;
  const dbStatus =
    dbState === 1
      ? "🟢 Connected".success
      : dbState === 2
      ? "🟡 Connecting".warn
      : "🔴 Disconnected".error;
  console.log(`   Status: ${dbStatus}`);

  console.log(`\n🔧 FEATURES ENABLED:`.verbose.bold);
  console.log(`   ✅ Enhanced Request Logging`.success);
  console.log(`   ✅ Page Transition Tracking`.success);
  console.log(`   ✅ Network Error Detection`.success);
  console.log(`   ✅ MongoDB Connection Monitoring`.success);
  console.log(`   ✅ Account Creation Error Handling`.success);
  console.log(`   ✅ Real-time Database Status`.success);

  console.log(`\n📡 MONITORING:`.warn.bold);
  console.log(`   🔍 All requests will be logged with full details`.warn);
  console.log(`   🔄 Page transitions will be tracked`.warn);
  console.log(`   🌐 Network errors will be detected and handled`.warn);
  console.log(`   🗄️  Database connection status will be monitored`.warn);

  console.log("\n" + "🚀".repeat(60).success);
  console.log("🎯 Server is ready to handle requests!".success.bold);
  console.log("🚀".repeat(60).success + "\n");
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    console.log("Process terminated");
  });
});

module.exports = { app, server, io };
