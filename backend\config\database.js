const mongoose = require("mongoose");

/**
 * Connect to MongoDB database
 * Uses connection string from environment variables
 */
const connectDB = async () => {
  try {
    // Check if MONGO_URI is provided
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI environment variable is not defined");
    }

    console.log("🔄 Connecting to MongoDB...");
    console.log(
      `📍 Database URI: ${process.env.MONGO_URI.replace(
        /\/\/.*@/,
        "//***:***@"
      )}`
    );

    const conn = await mongoose.connect(process.env.MONGO_URI, {
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
      maxPoolSize: 10, // Maintain up to 10 socket connections
    });

    console.log(`✅ MongoDB Connected Successfully!`);
    console.log(`📦 Host: ${conn.connection.host}`);
    console.log(`🗄️  Database: ${conn.connection.name}`);
    console.log(
      `🔗 Connection State: ${
        conn.connection.readyState === 1 ? "Connected" : "Disconnected"
      }`
    );

    // Handle connection events
    mongoose.connection.on("error", (err) => {
      console.error("❌ MongoDB connection error:", err.message);
    });

    mongoose.connection.on("disconnected", () => {
      console.log("⚠️  MongoDB disconnected. Attempting to reconnect...");
    });

    mongoose.connection.on("reconnected", () => {
      console.log("✅ MongoDB reconnected successfully");
    });

    mongoose.connection.on("connecting", () => {
      console.log("🔄 MongoDB connecting...");
    });

    // Graceful shutdown
    process.on("SIGINT", async () => {
      console.log("\n🔄 Gracefully shutting down...");
      await mongoose.connection.close();
      console.log("✅ MongoDB connection closed through app termination");
      process.exit(0);
    });

    process.on("SIGTERM", async () => {
      console.log("\n🔄 Gracefully shutting down...");
      await mongoose.connection.close();
      console.log("✅ MongoDB connection closed through app termination");
      process.exit(0);
    });

    return conn;
  } catch (error) {
    console.error("❌ Database connection failed:");
    console.error(`   Error: ${error.message}`);

    if (
      error.message.includes("ENOTFOUND") ||
      error.message.includes("ECONNREFUSED")
    ) {
      console.error("   💡 Possible solutions:");
      console.error("      - Check if MongoDB is running locally");
      console.error("      - Verify your MONGO_URI in the .env file");
      console.error(
        "      - For MongoDB Atlas, check your network access settings"
      );
    }

    if (error.message.includes("authentication failed")) {
      console.error("   💡 Authentication issue:");
      console.error("      - Check your MongoDB username and password");
      console.error("      - Verify database user permissions");
    }

    throw error; // Re-throw to be caught by server.js
  }
};

module.exports = connectDB;
