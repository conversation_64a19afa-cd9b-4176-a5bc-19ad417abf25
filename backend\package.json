{"name": "planit-backend", "version": "1.0.0", "description": "Backend API for Planit - Collaborative Trip Planner", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "build": "echo 'No build step required for Node.js backend'"}, "keywords": ["trip-planner", "collaborative", "travel", "nodejs", "express", "mongodb"], "author": "Planit Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}